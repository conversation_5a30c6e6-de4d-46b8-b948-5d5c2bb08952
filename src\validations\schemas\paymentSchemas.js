/**
 * Payment validation schemas
 */
const Joi = require('joi');

module.exports = {
  createPayment: {
    body: Joi.object({
      shopId: Joi.string().required()
        .messages({
          'string.empty': 'Shop ID is required',
          'any.required': 'Shop ID is required'
        }),
      customerId: Joi.string().required()
        .messages({
          'string.empty': 'Customer ID is required',
          'any.required': 'Customer ID is required'
        }),
      customerName: Joi.string().allow('').optional(),
      
      // Payment context enum
      paymentContext: Joi.string().valid(
        'debt',
        'subscription'
      ).required()
        .messages({
          'any.only': 'Payment context must be one of: debt, subscription',
          'any.required': 'Payment context is required'
        }),
      
      // Context-specific fields
      debtId: Joi.string().trim()
        .when('paymentContext', {
          is: 'debt',
          then: Joi.required(),
          otherwise: Joi.optional()
        })
        .messages({
          'string.empty': 'Debt ID cannot be empty if provided',
          'any.required': 'Debt ID is required for debt payments'
        }),
      
      subscriptionId: Joi.string().trim()
        .when('paymentContext', {
          is: 'subscription',
          then: Joi.required(),
          otherwise: Joi.optional()
        })
        .messages({
          'string.empty': 'Subscription ID cannot be empty if provided',
          'any.required': 'Subscription ID is required for subscription payments'
        }),
      
      amount: Joi.number().positive().required()
        .messages({
          'number.base': 'Amount must be a number',
          'number.positive': 'Amount must be positive',
          'any.required': 'Amount is required'
        }),
      
      method: Joi.string().valid('cash', 'bank_transfer', 'mobile_money', 'card', 'other').default('cash')
        .messages({
          'any.only': 'Payment method must be one of: cash, bank_transfer, mobile_money, card, other'
        }),
      
      referenceNumber: Joi.string().trim().allow('').optional(),
      notes: Joi.string().allow('').optional(),
      
      // Discount code validation
      discountCode: Joi.string().trim().allow('').optional()
        .messages({
          'string.empty': 'Discount code cannot be empty if provided'
        }),
      
      // Time fields
      paymentDate: Joi.date().iso().optional()
        .messages({
          'date.base': 'Payment date must be a valid date',
          'date.format': 'Payment date must be in ISO format'
        }),
      
      paidAtReal: Joi.date().iso().optional()
        .messages({
          'date.base': 'Real payment date must be a valid date',
          'date.format': 'Real payment date must be in ISO format'
        })
    })
  },
  
  updatePayment: {
    params: Joi.object({
      paymentId: Joi.string().required()
        .messages({
          'string.empty': 'Payment ID is required',
          'any.required': 'Payment ID is required'
        })
    }),
    body: Joi.object({
      amount: Joi.number().positive().optional()
        .messages({
          'number.base': 'Amount must be a number',
          'number.positive': 'Amount must be positive'
        }),
      
      method: Joi.string().valid('cash', 'bank_transfer', 'mobile_money', 'card', 'other').optional()
        .messages({
          'any.only': 'Payment method must be one of: cash, bank_transfer, mobile_money, card, other'
        }),
      
      referenceNumber: Joi.string().trim().allow('').optional(),
      notes: Joi.string().allow('').optional(),
      
      paymentDate: Joi.date().iso().optional()
        .messages({
          'date.base': 'Payment date must be a valid date',
          'date.format': 'Payment date must be in ISO format'
        }),
      
      paidAtReal: Joi.date().iso().optional()
        .messages({
          'date.base': 'Real payment date must be a valid date',
          'date.format': 'Real payment date must be in ISO format'
        })
    }).min(1)
  },
  
  getPayment: {
    params: Joi.object({
      paymentId: Joi.string().required()
        .messages({
          'string.empty': 'Payment ID is required',
          'any.required': 'Payment ID is required'
        })
    })
  },
  
  getAllPayments: {
    query: Joi.object({
      page: Joi.number().integer().min(1).default(1)
        .messages({
          'number.base': 'Page must be a number',
          'number.integer': 'Page must be an integer',
          'number.min': 'Page must be at least 1'
        }),
      limit: Joi.number().integer().min(1).max(100).default(10)
        .messages({
          'number.base': 'Limit must be a number',
          'number.integer': 'Limit must be an integer',
          'number.min': 'Limit must be at least 1',
          'number.max': 'Limit cannot exceed 100'
        }),
      
      // Filter options
      paymentContext: Joi.string().valid('debt', 'subscription').optional()
        .messages({
          'any.only': 'Payment context must be one of: debt, subscription'
        }),
      
      customerId: Joi.string().optional(),
      debtId: Joi.string().optional(),
      subscriptionId: Joi.string().optional(),
      
      method: Joi.string().valid('cash', 'bank_transfer', 'mobile_money', 'card', 'other').optional()
        .messages({
          'any.only': 'Payment method must be one of: cash, bank_transfer, mobile_money, card, other'
        }),
      
      startDate: Joi.date().iso().optional()
        .messages({
          'date.base': 'Start date must be a valid date',
          'date.format': 'Start date must be in ISO format'
        }),
      
      endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
        .messages({
          'date.base': 'End date must be a valid date',
          'date.format': 'End date must be in ISO format',
          'date.min': 'End date must be after start date'
        }),
      
      // SuperAdmin specific
      shopId: Joi.string().when('$role', {
        is: 'superAdmin',
        then: Joi.string().optional(),
        otherwise: Joi.forbidden()
      }).messages({
        'any.required': 'Shop ID is required for SuperAdmin to get payments'
      })
    })
  },
  
  deletePayment: {
    params: Joi.object({
      paymentId: Joi.string().required()
        .messages({
          'string.empty': 'Payment ID is required',
          'any.required': 'Payment ID is required'
        })
    })
  },
  
  // Bulk operations
  bulkCreatePayments: {
    body: Joi.object({
      payments: Joi.array().items(
        Joi.object({
          customerId: Joi.string().required(),
          paymentContext: Joi.string().valid('debt', 'subscription').required(),
          debtId: Joi.string().when('paymentContext', {
            is: 'debt',
            then: Joi.required(),
            otherwise: Joi.optional()
          }),
          subscriptionId: Joi.string().when('paymentContext', {
            is: 'subscription',
            then: Joi.required(),
            otherwise: Joi.optional()
          }),
          amount: Joi.number().positive().required(),
          method: Joi.string().valid('cash', 'bank_transfer', 'mobile_money', 'card', 'other').default('cash'),
          referenceNumber: Joi.string().allow('').optional(),
          notes: Joi.string().allow('').optional(),
          paymentDate: Joi.date().iso().optional(),
          paidAtReal: Joi.date().iso().optional()
        })
      ).min(1).max(50).required()
        .messages({
          'array.min': 'At least one payment is required',
          'array.max': 'Cannot process more than 50 payments at once',
          'any.required': 'Payments array is required'
        })
    })
  }
};
