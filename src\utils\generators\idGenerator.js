/**
 * Utility for generating unique IDs for various collections in DeynCare
 */

/**
 * Generates a unique ID with a prefix and padding
 * @param {string} prefix - The prefix for the ID (e.g., 'USR', 'SHOP')
 * @param {number} lastId - The last used ID number
 * @param {number} padLength - The length to pad the number to (default: 3)
 * @returns {string} - The formatted ID (e.g., 'USR001')
 */
const generateId = (prefix, lastId, padLength = 3) => {
  const nextId = lastId + 1;
  return `${prefix}${nextId.toString().padStart(padLength, '0')}`;
};

/**
 * ID generators for each collection
 */
module.exports = {
  generateUserId: async (User) => {
    const lastUser = await User.findOne({}, { userId: 1 }).sort({ createdAt: -1 });
    const lastId = lastUser ? parseInt(lastUser.userId.replace('USR', '')) : 0;
    return generateId('USR', lastId);
  },
  
  generateShopId: async (Shop) => {
    const lastShop = await Shop.findOne({}, { shopId: 1 }).sort({ createdAt: -1 });
    const lastId = lastShop ? parseInt(lastShop.shopId.replace('SHOP', '')) : 0;
    return generateId('SHOP', lastId);
  },
  
  generateSubscriptionId: async (Subscription) => {
    try {
      // Use atomic operation to prevent race conditions
      const lastSub = await Subscription.findOne({}, { subscriptionId: 1 }).sort({ createdAt: -1 }).lean();
      let lastId = 0;
      
      if (lastSub && lastSub.subscriptionId) {
        // Extract only the numeric part with regex for safety
        const matches = lastSub.subscriptionId.match(/SUB(\d+)/);
        if (matches && matches[1]) {
          const numericPart = matches[1];
          lastId = parseInt(numericPart, 10);
          
          // Additional validation to prevent NaN
          if (isNaN(lastId)) {
            console.warn(`Invalid subscription ID found: ${lastSub.subscriptionId}, defaulting to 0`);
            lastId = 0;
          }
        }
      }
      
      // Generate the next ID
      const nextId = generateId('SUB', lastId);
      
      // Check if this ID already exists (race condition protection)
      const existingSubscription = await Subscription.findOne({ subscriptionId: nextId }).lean();
      if (existingSubscription) {
        // If collision detected, use timestamp-based fallback immediately
        const timestamp = Date.now().toString().slice(-6);
        const randomSuffix = Math.random().toString(36).substr(2, 3).toUpperCase();
        return `SUB${timestamp}${randomSuffix}`;
      }
      
      return nextId;
    } catch (error) {
      console.error('Error generating subscription ID:', error);
      // Fallback: use timestamp-based ID to ensure uniqueness
      const timestamp = Date.now().toString().slice(-6);
      const randomSuffix = Math.random().toString(36).substr(2, 3).toUpperCase();
      return `SUB${timestamp}${randomSuffix}`;
    }
  },
  
  generateCustomerId: async (Customer) => {
    const lastCustomer = await Customer.findOne({}, { customerId: 1 }).sort({ createdAt: -1 });
    const lastId = lastCustomer ? parseInt(lastCustomer.customerId.replace('CUST', '')) : 0;
    return generateId('CUST', lastId);
  },
  
  generateDebtId: async (Debt) => {
    const lastDebt = await Debt.findOne({}, { debtId: 1 }).sort({ createdAt: -1 });
    const lastId = lastDebt ? parseInt(lastDebt.debtId.replace('DEBT', '')) : 0;
    return generateId('DEBT', lastId);
  },
  
  generatePaymentId: async (Payment) => {
    const lastPayment = await Payment.findOne({}, { paymentId: 1 }).sort({ createdAt: -1 });
    const lastId = lastPayment ? parseInt(lastPayment.paymentId.replace('PAY', '')) : 0;
    return generateId('PAY', lastId);
  },

  
  generateNotificationId: async (Notification) => {
    const lastNotification = await Notification.findOne({}, { notificationId: 1 }).sort({ createdAt: -1 });
    const lastId = lastNotification ? parseInt(lastNotification.notificationId.replace('NTF', '')) : 0;
    return generateId('NTF', lastId);
  },
  
  generateFileId: async (File) => {
    const lastFile = await File.findOne({}, { fileId: 1 }).sort({ createdAt: -1 });
    const lastId = lastFile ? parseInt(lastFile.fileId.replace('FILE', '')) : 0;
    return generateId('FILE', lastId);
  },
  
  generateSessionId: async (Session) => {
    const lastSession = await Session.findOne({}, { sessionId: 1 }).sort({ createdAt: -1 });
    const lastId = lastSession ? parseInt(lastSession.sessionId.replace('SESS', '')) : 0;
    return generateId('SESS', lastId);
  },
  
  generateFinancialSnapshotId: async (FinancialSnapshot) => {
    const lastSnapshot = await FinancialSnapshot.findOne({}, { snapshotId: 1 }).sort({ generatedAt: -1 });
    const lastId = lastSnapshot ? parseInt(lastSnapshot.snapshotId.replace('FS', '')) : 0;
    return generateId('FS', lastId);
  },
  
  generateReportId: async (Report) => {
    const lastReport = await Report.findOne({}, { reportId: 1 }).sort({ createdAt: -1 });
    const lastId = lastReport ? parseInt(lastReport.reportId.replace('REPORT', '')) : 0;
    return generateId('REPORT', lastId);
  },
  
  /**
   * Generates a unique plan ID
   * @param {Object} Plan - The Plan model
   * @returns {Promise<string>} - The generated plan ID
   */
  generatePlanId: async (Plan) => {
    const lastPlan = await Plan.findOne({}, { planId: 1 }).sort({ createdAt: -1 });
    const lastId = lastPlan ? parseInt(lastPlan.planId.replace('PLAN', '')) : 0;
    return generateId('PLAN', lastId);
  },
  
  /**
   * Generates a unique discount code ID
   * @param {Object} DiscountCode - The DiscountCode model
   * @returns {Promise<string>} - The generated discount ID (e.g., 'DSC001')
   */
  generateDiscountId: async (DiscountCode) => {
    try {
      const lastDiscount = await DiscountCode.findOne({}, { discountId: 1 }).sort({ createdAt: -1 });
      
      let lastId = 0;
      if (lastDiscount && lastDiscount.discountId) {
        // Extract only the numeric part with regex and parse it
        const matches = lastDiscount.discountId.match(/DSC(\d+)/);
        if (matches && matches[1]) {
          lastId = parseInt(matches[1]);
          // If parsing fails, default to 0
          if (isNaN(lastId)) lastId = 0;
        }
      }
      
      return generateId('DSC', lastId);
    } catch (error) {
      console.error('Error generating discount ID:', error);
      // Fallback to a safe default
      return 'DSC001';
    }
  },
  
  /**
   * Simple synchronous ID generator with prefix
   * Used for situations where async DB lookup isn't possible
   * @param {string} prefix - The prefix for the ID
   * @returns {string} - A random ID with the given prefix
   */
  generateSimpleId: (prefix) => {
    const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${prefix}${randomPart}`;
  },

  /**
   * Generates a unique app upload ID
   * @param {Object} AppUpload - The AppUpload model
   * @returns {Promise<string>} - The generated upload ID (e.g., 'APP001')
   */
  generateAppUploadId: async (AppUpload) => {
    try {
      const lastUpload = await AppUpload.findOne({}, { uploadId: 1 }).sort({ createdAt: -1 });
      
      let lastId = 0;
      if (lastUpload && lastUpload.uploadId) {
        // Extract only the numeric part with regex and parse it
        const matches = lastUpload.uploadId.match(/APP(\d+)/);
        if (matches && matches[1]) {
          lastId = parseInt(matches[1]);
          // If parsing fails, default to 0
          if (isNaN(lastId)) lastId = 0;
        }
      }
      
      return generateId('APP', lastId);
    } catch (error) {
      console.error('Error generating app upload ID:', error);
      // Fallback to a safe default using timestamp
      const timestamp = Date.now().toString().slice(-4);
      return `APP${timestamp}`;
    }
  }
};

