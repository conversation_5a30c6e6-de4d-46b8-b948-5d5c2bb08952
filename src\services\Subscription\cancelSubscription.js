/**
 * Cancel Subscription Service
 * Handles subscription cancellation logic
 */
const { Subscription, Shop } = require('../../models');
const Plan = require('../../models/Plan.model');
const SubscriptionEmailService = require('../email/subscriptionEmailService');
const { AppError, logError, logSuccess } = require('../../utils');

/**
 * Cancel a subscription
 * @param {string} subscriptionId - ID of the subscription to cancel
 * @param {Object} cancellationData - Cancellation data (reason, feedback, etc.)
 * @param {Object} options - Additional options (actorId, actorRole, etc.)
 * @returns {Promise<Object>} Updated subscription
 */
const cancelSubscription = async (subscriptionId, cancellationData = {}, options = {}) => {
  try {
    const { reason = 'user_request', feedback, immediateEffect = false } = cancellationData;
    const { actorId = 'system', actorRole = 'system' } = options;

    // Find the subscription
    const subscription = await Subscription.findOne({ 
      subscriptionId, 
      isDeleted: false 
    }).populate('plan');

    if (!subscription) {
      throw new AppError('Subscription not found', 404, 'subscription_not_found');
    }

    // Check if subscription is already canceled
    if (subscription.status === 'canceled') {
      throw new AppError('Subscription is already canceled', 400, 'already_canceled');
    }

    // Update subscription status
    subscription.status = 'canceled';
    subscription.cancellation = {
      reason,
      feedback,
      canceledAt: new Date(),
      canceledBy: actorId,
      actorRole
    };

    // If immediate effect is requested, set end date to now
    if (immediateEffect) {
      subscription.dates.endDate = new Date();
    } else {
      // Otherwise, keep the current end date (subscription remains active until end date)
      // This allows users to continue using the service until the end of their billing period
    }

    // Add to history
    subscription.history.push({
      action: 'subscription_canceled',
      date: new Date(),
      performedBy: actorId,
      details: {
        reason,
        feedback,
        immediateEffect
      }
    });

    // Save the updated subscription
    await subscription.save();

    logSuccess(`Subscription ${subscriptionId} canceled successfully by ${actorId}`, 'SubscriptionService');

    // --- NEW LOGIC: Set shop to free trial and send cancellation email ---
    // Fetch the shop
    const shop = await Shop.findOne({ shopId: subscription.shopId });
    if (shop) {
      // Check if shop already has a trial subscription (active or not expired)
      const existingTrial = await Subscription.findOne({
        shopId: shop.shopId,
        'plan.type': 'trial',
        status: { $in: ['trial', 'active'] },
        isDeleted: false,
        'dates.endDate': { $gte: new Date() }
      });
      let trialSubscription;
      if (!existingTrial) {
        // Find the trial plan
        const trialPlan = await Plan.findOne({ type: 'trial' });
        if (trialPlan) {
          // Create a new trial subscription
          trialSubscription = new Subscription({
            subscriptionId: `TRIAL-${shop.shopId}-${Date.now()}`,
            shopId: shop.shopId,
            planId: trialPlan.planId,
            plan: {
              name: trialPlan.name,
              type: trialPlan.type
            },
            pricing: {
              basePrice: 0,
              currency: trialPlan.pricing.currency || 'USD',
              billingCycle: 'one-time'
            },
            status: 'trial',
            payment: {
              method: 'free',
              verified: true
            },
            dates: {
              startDate: new Date(),
              endDate: (() => {
                const end = new Date();
                end.setDate(end.getDate() + (trialPlan.pricing.trialDays || 14));
                return end;
              })(),
              trialEndsAt: (() => {
                const end = new Date();
                end.setDate(end.getDate() + (trialPlan.pricing.trialDays || 14));
                return end;
              })()
            },
            renewalSettings: {
              autoRenew: false
            },
            history: [{
              action: 'trial_started',
              date: new Date()
            }]
          });
          await trialSubscription.save();
        }
      }
      // Update shop's currentSubscriptionId
      const newTrialId = (existingTrial ? existingTrial.subscriptionId : trialSubscription ? trialSubscription.subscriptionId : null);
      if (newTrialId) {
        shop.currentSubscriptionId = newTrialId;
        await shop.save();
      }
      // Send cancellation email
      try {
        await SubscriptionEmailService.sendSubscriptionCanceledEmail({
          email: shop.email,
          shopName: shop.shopName,
          endDate: subscription.dates.endDate,
          immediateEffect,
          planType: subscription.plan?.type || 'Standard'
        });
      } catch (emailErr) {
        logError('Failed to send cancellation email', 'SubscriptionService', emailErr);
      }
    }
    // --- END NEW LOGIC ---

    return subscription;
  } catch (error) {
    logError(`Failed to cancel subscription ${subscriptionId}`, 'SubscriptionService', error);
    throw error;
  }
};

module.exports = cancelSubscription; 