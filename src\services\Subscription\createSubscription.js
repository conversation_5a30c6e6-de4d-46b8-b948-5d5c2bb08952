const { Subscription, Plan } = require('../../models');
const { AppError, idGenerator, logSuccess, logError } = require('../../utils');

/**
 * Create a subscription record for a shop
 * @param {Object} subscriptionData - Subscription data
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Created subscription
 */
const createSubscription = async (subscriptionData, options = {}) => {
  try {
    const {
      shopId,
      planType = 'trial',
      planId = null,
      pricing = {},
      paymentMethod = 'free',
      paymentDetails = null,
      discountDetails = null,
      session = null
    } = subscriptionData;

    if (!shopId) {
      throw new AppError('Shop ID is required to create subscription', 400, 'missing_shop_id');
    }

    // Generate unique subscription ID using timestamp + random
    const subscriptionId = `SUB_${Date.now()}_${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
    
    // Create subscription object
    const subscription = new Subscription({
      subscriptionId,
      shopId,
      planType,
      planId,
      pricing,
      paymentMethod,
      paymentDetails,
      discountDetails,
      status: 'active',
      createdAt: new Date()
    });

    // Save with session if provided
    if (session) {
      await subscription.save({ session });
    } else {
      await subscription.save();
    }

    logSuccess(`Subscription created: ${subscriptionId} for shop: ${shopId}`, 'SubscriptionService');
    return subscription;

  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Error creating subscription: ${error.message}`, 'SubscriptionService', error);
    throw new AppError('Failed to create subscription', 500, 'subscription_creation_error');
  }
};

module.exports = createSubscription; 
