/**
 * Initialize Plans Service
 * 
 * Creates default plans if none exist
 * Matches backend initializePlans.js functionality
 */

import { handleError, processApiResponse, logApiCall } from '../baseService';
import getPlans from './getPlans';
import createPlan from './createPlan';

/**
 * Initialize default plans
 * Creates default plans if collection is empty (matches backend logic)
 * @returns {Promise<Object>} API response with created/existing plans
 */
async function initializePlans() {
  try {
    logApiCall('PlanService.initializePlans', 'initialize-default-plans');

    // Check if any plans exist
    const existingPlansResponse = await getPlans();
    
    if (!existingPlansResponse.success) {
      throw new Error('Failed to check existing plans');
    }

    const existingPlans = existingPlansResponse.data || [];

    // If plans already exist, don't create defaults
    if (existingPlans.length > 0) {
      return {
        success: true,
        message: 'Default plans already exist',
        data: existingPlans,
        created: false
      };
    }

    // Create default plans that EXACTLY match backend defaults
    const defaultPlans = [
      {
        name: 'trial',
        type: 'trial',
        displayName: 'Free Trial',
        description: 'Try all features free for 14 days',
        pricing: {
          basePrice: 0,
          currency: 'USD',
          billingCycle: 'one-time',
          trialDays: 14,
          setupFee: 0
        },
        features: {
          debtTracking: true,
          customerPayments: true,
          smsReminders: true,
          smartRiskScore: true,
          businessDashboard: true,
          exportReports: true,
          customerProfiles: true,
          offlineSupport: true
        },
        limits: {
          maxProducts: 1000,
          maxEmployees: 10,
          maxStorageMB: 500,
          maxCustomers: 1000,
          maxDailyTransactions: 500
        },
        isActive: true,
        displayOrder: 1,
        metadata: {
          isRecommended: false,
          tags: ['trial', 'free'],
          customFields: {}
        }
      },
      {
        name: 'monthly',
        type: 'monthly',
        displayName: 'Monthly Plan',
        description: 'Full access to all features with monthly billing',
        pricing: {
          basePrice: 10,
          currency: 'USD',
          billingCycle: 'monthly',
          trialDays: 0,
          setupFee: 0
        },
        features: {
          debtTracking: true,
          customerPayments: true,
          smsReminders: true,
          smartRiskScore: true,
          businessDashboard: true,
          exportReports: true,
          customerProfiles: true,
          offlineSupport: true
        },
        limits: {
          maxProducts: 1000,
          maxEmployees: 10,
          maxStorageMB: 500,
          maxCustomers: 1000,
          maxDailyTransactions: 500
        },
        isActive: true,
        displayOrder: 2,
        metadata: {
          isRecommended: true,
          tags: ['monthly', 'standard'],
          customFields: {}
        }
      },
      {
        name: 'yearly',
        type: 'yearly',
        displayName: 'Annual Plan',
        description: 'Save 20% with yearly billing',
        pricing: {
          basePrice: 8,
          currency: 'USD',
          billingCycle: 'yearly',
          trialDays: 0,
          setupFee: 0
        },
        features: {
          debtTracking: true,
          customerPayments: true,
          smsReminders: true,
          smartRiskScore: true,
          businessDashboard: true,
          exportReports: true,
          customerProfiles: true,
          offlineSupport: true
        },
        limits: {
          maxProducts: 1000,
          maxEmployees: 10,
          maxStorageMB: 500,
          maxCustomers: 1000,
          maxDailyTransactions: 500
        },
        isActive: true,
        displayOrder: 3,
        metadata: {
          isRecommended: false,
          tags: ['yearly', 'discounted'],
          customFields: {}
        }
      }
    ];

    // Create each default plan
    const createdPlans = [];
    const errors = [];

    for (const planData of defaultPlans) {
      try {
        const result = await createPlan(planData);
        if (result.success) {
          createdPlans.push(result.data);
        } else {
          errors.push(`Failed to create ${planData.name} plan: ${result.message}`);
        }
      } catch (error) {
        errors.push(`Failed to create ${planData.name} plan: ${error.message}`);
        console.warn(`Failed to create default plan ${planData.name}:`, error.message);
      }
    }

    return {
      success: true,
      message: `Created ${createdPlans.length} default plans`,
      data: createdPlans,
      created: true,
      errors: errors.length > 0 ? errors : undefined
    };
  } catch (error) {
    handleError(error, 'PlanService.initializePlans', true);
    throw error;
  }
}

export default initializePlans; 