const Joi = require('joi');
const patterns = require('../validationPatterns');

/**
 * Registration system validation schemas
 * Covers public registration and SuperAdmin registration management
 */
const registerSchemas = {
  /**
   * Schema for initial registration (public)
   * POST /api/register/init
   */
  initRegistration: Joi.object({
    // User data - EXACT FLUTTER MATCH
    fullName: patterns.string.fullName.required(),
    email: patterns.string.email.required(),
    phone: patterns.string.phone.required(),
    password: patterns.string.password.required(),
    
    // Shop data - EXACT FLUTTER MATCH
    shopName: patterns.string.shopName.required(),
    shopAddress: patterns.string.shopAddress.required(),
    
    // Subscription data - EXACT FLUTTER MATCH
    planType: Joi.string().valid('trial', 'monthly', 'yearly').default('trial'),
    paymentMethod: Joi.string().valid('offline', 'EVC Plus', 'Card', 'Mobile Money', 'Cash', 'Bank Transfer').default('offline'),
    initialPaid: Joi.alternatives().try(
      Joi.boolean(),
      Joi.string().valid('true', 'false').custom((value) => value === 'true')
    ).default(false), // Flutter sends as string, convert to boolean
    
    // Optional fields - EXACT FLUTTER MATCH
    discountCode: Joi.string().optional(),
    
    // Backend-only fields (NOT sent by Flutter - so optional/default)
    businessCategory: Joi.string().valid(
      'general_store',
      'grocery', 
      'electronics',
      'clothing',
      'restaurant',
      'pharmacy',
      'hardware',
      'automotive',
      'beauty_salon',
      'others'
    ).default('general_store').optional(),
    businessType: Joi.string().valid('retail', 'wholesale', 'service', 'manufacturing', 'restaurant', 'other').default('retail').optional(),
    registeredBy: Joi.string().valid('self', 'superAdmin').default('self').optional(),
    paymentDetails: Joi.object().optional()
  }),

  /**
   * Schema for email verification
   * POST /api/register/verify-email
   */
  verifyEmail: Joi.object({
    email: patterns.string.email.required(),
    verificationCode: Joi.string().length(6).required().messages({
      'string.length': 'Verification code must be exactly 6 characters',
      'any.required': 'Verification code is required'
    })
  }),

  /**
   * Schema for resending verification email
   * POST /api/register/resend-verification
   */
  resendVerification: Joi.object({
    email: patterns.string.email.required()
  }),

  /**
   * Schema for payment processing - Enhanced to handle both online and offline payments
   * POST /api/register/pay
   */
  processPayment: Joi.object({
    planType: Joi.string().valid('trial', 'monthly', 'yearly').required(),
    paymentMethod: Joi.string().valid('EVC Plus', 'Card', 'Mobile Money', 'Cash', 'Bank Transfer', 'offline').required(),
    paymentDetails: Joi.object().when('paymentMethod', {
      is: 'EVC Plus',
      then: Joi.object({
        phoneNumber: patterns.string.phone.required()
      }).required(),
      otherwise: Joi.object().optional()
    }),
    discountCode: Joi.string().optional(),
    
    // Enhanced offline payment fields (optional for all payment methods)
    payerName: Joi.string().min(2).max(100).optional(),
    payerPhone: patterns.string.phone.optional(),
    notes: Joi.string().max(1000).allow('').optional(),
    bankDetails: Joi.string().max(500).allow('').optional(),
    transferReference: Joi.string().max(100).allow('').optional()
  }),

  // ============================================
  // SuperAdmin Registration Management Schemas
  // ============================================

  /**
   * Schema for SuperAdmin creating shop (SAME payload as public registration)
   * POST /api/register/admin/create-shop
   */
  superAdminCreateShop: Joi.object({
    // User data (same as public registration)
    fullName: patterns.string.fullName.required(),
    email: patterns.string.email.required(),
    phone: patterns.string.phone.required(),
    password: patterns.string.password.required(),
    
    // Shop data (same as public registration)
    shopName: patterns.string.shopName.required(),
    shopAddress: patterns.string.shopAddress.required(),
    
    // Business category data (same as public registration)
    businessCategory: Joi.string().valid(
      'general_store',
      'grocery', 
      'electronics',
      'clothing',
      'restaurant',
      'pharmacy',
      'hardware',
      'automotive',
      'beauty_salon',
      'others'
    ).default('general_store').messages({
      'any.only': 'Business category must be one of: general_store, grocery, electronics, clothing, restaurant, pharmacy, hardware, automotive, beauty_salon, others'
    }),
    businessType: Joi.string().valid('retail', 'wholesale', 'service', 'manufacturing', 'restaurant', 'other').default('retail'),
    
    // Subscription data (same as public registration)
    planType: Joi.string().valid('trial', 'monthly', 'yearly').default('monthly'),
    registeredBy: Joi.string().valid('self', 'superAdmin').default('superAdmin'),
    paymentMethod: Joi.string().valid('offline', 'EVC Plus', 'Card', 'Mobile Money', 'Cash', 'Bank Transfer', 'admin_created').default('admin_created'),
    initialPaid: Joi.boolean().default(true),
    
    // Optional (same as public registration)
    paymentDetails: Joi.object().optional(),
    discountCode: Joi.string().optional()
  }),

  /**
   * Schema for SuperAdmin creating admin for existing shop
   * POST /api/register/admin/create-admin
   */
  superAdminCreateAdmin: Joi.object({
    shopId: Joi.string().required().messages({
      'any.required': 'Shop ID is required'
    }),
    adminFullName: patterns.string.fullName.required(),
    adminEmail: patterns.string.email.required(),
    adminPhone: patterns.string.phone.required(),
    adminPassword: patterns.string.password.required(),
    replaceExistingAdmin: Joi.boolean().default(false)
  }),

  /**
   * Schema for SuperAdmin approving shop registration
   * POST /api/register/admin/approve-shop/:shopId
   * UPDATED: Added offline payment confirmation fields
   */
  superAdminApproveShop: Joi.object({
    approvalNotes: Joi.string().max(500).optional(),
    activateImmediately: Joi.boolean().default(true),
    // NEW: Offline payment confirmation
    confirmOfflinePayment: Joi.boolean().default(true)
      .messages({
        'boolean.base': 'confirmOfflinePayment must be a boolean'
      }),
    offlinePaymentDetails: Joi.object({
      receiptNumber: Joi.string().optional(),
      paymentDate: Joi.date().optional(),
      amount: Joi.number().positive().optional(),
      currency: Joi.string().valid('USD', 'SOS').default('USD'),
      paymentMethod: Joi.string().valid('Cash', 'Bank Transfer', 'Check', 'Other').optional(),
      notes: Joi.string().max(300).optional(),
      verifiedBy: Joi.string().optional()
    }).optional().default({})
      .messages({
        'object.base': 'offlinePaymentDetails must be an object'
      })
  })
};

module.exports = registerSchemas; 