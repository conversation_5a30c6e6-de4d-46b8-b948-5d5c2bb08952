module.exports = {
  apps: [{
    name: 'deyncare-backend',
    script: './server.js',
    instances: 1,
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '512M',
    
    // Environment variables
    env: {
      NODE_ENV: 'production',
      PORT: 5000,
      
      // Database
      MONGODB_URI: process.env.MONGODB_URI,
      
      // JWT Configuration
      JWT_ACCESS_SECRET: process.env.JWT_ACCESS_SECRET || 'your_super_secure_access_token_secret_key',
      JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET || 'your_super_secure_refresh_token_secret_key',
      JWT_ACCESS_EXPIRY: '2h',    // Fixed: 2 hours as intended
      JWT_REFRESH_EXPIRY: '30d',
      
      // CORS Configuration
      CORS_ORIGIN: 'https://deyncare.cajiibcreative.com,https://khanciye.com',
      
      // Email Configuration
      EMAIL_HOST: 'smtp.gmail.com',
      EMAIL_PORT: 587,
      EMAIL_USER: process.env.EMAIL_USER,
      EMAIL_PASS: process.env.EMAIL_PASS,
      EMAIL_FROM: process.env.EMAIL_FROM,
      
      // Admin Configuration
      CREATE_SUPER_ADMIN: 'false',
      SUPER_ADMIN_EMAIL: process.env.SUPER_ADMIN_EMAIL,
      SUPER_ADMIN_PASSWORD: process.env.SUPER_ADMIN_PASSWORD,
      SUPER_ADMIN_NAME: process.env.SUPER_ADMIN_NAME,
      SUPER_ADMIN_PHONE: process.env.SUPER_ADMIN_PHONE,
      
      // Features
      ENABLE_SCHEDULER: 'true',
      
      // Security
      COOKIE_SECRET: process.env.COOKIE_SECRET || 'deyncare-secret-key',
      SESSION_SECRET: process.env.SESSION_SECRET || 'deyncare-session-secret',
      SECURE_COOKIES: 'true',
      
      // Logging
      LOG_LEVEL: 'info'
    },
    
    // Logging configuration
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    
    // Auto restart configuration
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    
    // Graceful shutdown
    kill_timeout: 5000,
    listen_timeout: 3000,
    
    // Health check
    health_check_grace_period: 3000
  }],
  
  deploy: {
    production: {
      user: 'root',
      host: 'your-vps-ip',
      ref: 'origin/main',
      repo: 'https://github.com/abdinajib716/DeynCare-Backend.git',
      path: '/var/www/deyncare-backend.khanciye.com',
      'post-deploy': 'npm install --production && pm2 reload ecosystem.config.js --env production && pm2 save'
    }
  }
}; 