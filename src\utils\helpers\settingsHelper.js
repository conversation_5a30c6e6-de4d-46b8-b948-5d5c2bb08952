/**
 * Settings Helper
 * Provides utility functions for managing system settings
 */
const Setting = require('../../models/setting.model');
const { logInfo, logError, logSuccess } = require('../logger');
const AppError = require('../core/AppError');

/**
 * Default payment settings to initialize the system with
 */
/**
 * Default general system settings
 */
const DEFAULT_GENERAL_SETTINGS = [
  {
    key: 'app_name',
    category: 'system',
    displayName: 'Application Name',
    description: 'The name of the application displayed in the UI',
    value: 'DeynCare',
    dataType: 'string',
    defaultValue: 'DeynCare',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system',
    validation: {
      minLength: 1,
      maxLength: 50
    }
  },
  {
    key: 'company_name',
    category: 'system',
    displayName: 'Company Name',
    description: 'The name of the company using the system',
    value: '',
    dataType: 'string',
    defaultValue: '',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system',
    validation: {
      maxLength: 100
    }
  },
  {
    key: 'company_email',
    category: 'system',
    displayName: 'Company Email',
    description: 'Primary contact email for the company',
    value: '',
    dataType: 'string',
    defaultValue: '',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system',
    validation: {
      pattern: '^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' // Allow empty string OR valid email
    }
  },
  {
    key: 'company_phone',
    category: 'system',
    displayName: 'Company Phone',
    description: 'Primary contact phone number for the company',
    value: '',
    dataType: 'string',
    defaultValue: '',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system',
    validation: {
      maxLength: 20
    }
  },
  {
    key: 'company_address',
    category: 'system',
    displayName: 'Company Address',
    description: 'Physical address of the company',
    value: '',
    dataType: 'string',
    defaultValue: '',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system',
    validation: {
      maxLength: 200
    }
  },
  {
    key: 'maintenance_mode',
    category: 'system',
    displayName: 'Maintenance Mode',
    description: 'Enables or disables maintenance mode for the system',
    value: false,
    dataType: 'boolean',
    defaultValue: false,
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'enable_notifications',
    category: 'system',
    displayName: 'Enable Notifications',
    description: 'Enables or disables system notifications',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'default_currency',
    category: 'system',
    displayName: 'Default Currency',
    description: 'Default currency used throughout the system',
    value: 'USD',
    dataType: 'string',
    defaultValue: 'USD',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system',
    validation: {
      minLength: 3,
      maxLength: 3,
      enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'SOS'] // Common currencies + Somali Shilling
    }
  }
];

const DEFAULT_ML_SETTINGS = [
  {
    key: 'ml_enabled',
    category: 'ml',
    displayName: 'Enable ML Engine',
    description: 'Enables or disables the ML engine for the entire platform.',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'ml_api_base_url',
    category: 'ml',
    displayName: 'ML API Base URL',
    description: 'Full FastAPI base URL for ML engine (e.g., http://ml-api.deyncare.com).',
    value: '',
    dataType: 'string',
    defaultValue: '',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'ml_api_key',
    category: 'ml',
    displayName: 'ML API Key',
    description: 'API key used to authorize requests to the ML server.',
    value: '',
    dataType: 'string',
    defaultValue: '',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: false,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'ml_predict_endpoint',
    category: 'ml',
    displayName: 'ML Predict Endpoint',
    description: 'Path for single prediction (e.g., /predict_single/).',
    value: '/predict_single/',
    dataType: 'string',
    defaultValue: '/predict_single/',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'ml_auto_trigger_on_due',
    category: 'ml',
    displayName: 'Auto Trigger ML on Due',
    description: 'Triggers ML when due date passes (optional if hardcoded in logic).',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'ml_auto_trigger_on_payment_update',
    category: 'ml',
    displayName: 'Auto Trigger ML on Payment Update',
    description: 'Triggers ML when payment is recorded (optional if hardcoded).',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'store_risk_score_in_db',
    category: 'ml',
    displayName: 'Store Risk Score in DB',
    description: 'Controls if the backend saves risk scores into MongoDB.',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'risk_data_retention_days',
    category: 'ml',
    displayName: 'Risk Data Retention Days',
    description: 'How long to keep saved risk results (e.g., 365 days).',
    value: 365,
    dataType: 'number',
    defaultValue: 365,
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'ml_prediction_timeout',
    category: 'ml',
    displayName: 'ML Prediction Timeout',
    description: 'Timeout for ML API request in seconds.',
    value: 10,
    dataType: 'number',
    defaultValue: 10,
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  }
];

const DEFAULT_PAYMENT_SETTINGS = [
  // EVC Payment Credential Settings
  {
    key: 'evc_credential_requirements',
    category: 'payment',
    displayName: 'EVC Credential Requirements',
    description: 'Validation requirements for EVC payment credentials',
    value: {
      MERCHANT_U_ID: {
        minLength: 6,
        maxLength: 20,
        required: true
      },
      API_USER_ID: {
        minLength: 6, 
        maxLength: 20,
        required: true
      },
      API_KEY: {
        minLength: 10,
        maxLength: 64,
        required: true
      },
      MERCHANT_NO: {
        minLength: 8,
        maxLength: 15,
        required: true
      },
      URL: {
        minLength: 10,
        maxLength: 255,
        required: true,
        pattern: '^https?://'
      }
    },
    dataType: 'object',
    defaultValue: {},
    accessLevel: 'superAdmin',
    isEditable: false,
    isVisible: false,
    shopId: null,
    updatedBy: 'system'
  },
  // Global payment method switches
  {
    key: 'enable_online_payment',
    category: 'payment',
    displayName: 'Enable Online Payments',
    description: 'Master switch for all API payment integrations',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null, // Global setting
    updatedBy: 'system'
  },
  {
    key: 'enable_offline_payment',
    category: 'payment',
    displayName: 'Enable Offline Payments',
    description: 'Global toggle for bank/cash/manual uploads',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null, // Global setting
    updatedBy: 'system'
  },
  {
    key: 'require_receipt_proof_offline',
    category: 'payment',
    displayName: 'Require Receipt for Offline Payments',
    description: 'Whether to require proof of payment for offline payment methods',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null, // Global setting
    updatedBy: 'system'
  },
  
  // Available payment methods
  {
    key: 'payment_methods_available',
    category: 'payment',
    displayName: 'Available Payment Methods',
    description: 'List of all payment methods available in the system',
    value: ['Cash', 'EVC Plus', 'Bank Transfer', 'Mobile Money', 'Check', 'Card', 'Other', 'offline'],
    dataType: 'array',
    defaultValue: ['Cash', 'EVC Plus', 'Bank Transfer', 'offline'],
    validation: {
      minItems: 1
    },
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null, // Global setting
    updatedBy: 'system'
  },
  
  // Specific payment methods for subscription payments
  {
    key: 'subscription_payment_methods',
    category: 'payment',
    displayName: 'Subscription Payment Methods',
    description: 'Payment methods available for subscription payments',
    value: ['Cash', 'EVC Plus', 'Bank Transfer', 'Mobile Money', 'offline'],
    dataType: 'array',
    defaultValue: ['Cash', 'EVC Plus', 'offline'],
    validation: {
      minItems: 1
    },
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null, // Global setting
    updatedBy: 'system'
  }
];

/**
 * Default notification settings for email, SMS, and Firebase push notifications
 */
const DEFAULT_NOTIFICATION_SETTINGS = [
  // Email Notification Settings
  {
    key: 'email_notifications_enabled',
    category: 'notification',
    displayName: 'Enable Email Notifications',
    description: 'Master switch for all email notifications',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'email_templates_enabled',
    category: 'notification',
    displayName: 'Enable Email Templates',
    description: 'Use system email templates for notifications',
    value: true,
    dataType: 'boolean',
    defaultValue: true,
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },

  // SMS Notification Settings (Hormuud Provider)
  {
    key: 'sms_notifications_enabled',
    category: 'notification',
    displayName: 'Enable SMS Notifications',
    description: 'Master switch for SMS notifications via Hormuud provider',
    value: false,
    dataType: 'boolean',
    defaultValue: false,
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'sms_api_base_url',
    category: 'notification',
    displayName: 'SMS API Base URL',
    description: 'Hormuud SMS API endpoint',
    value: 'https://smsapi.hormuud.com/api/sms/send',
    dataType: 'string',
    defaultValue: 'https://smsapi.hormuud.com/api/sms/send',
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'sms_credentials',
    category: 'notification',
    displayName: 'SMS Credentials',
    description: 'Encrypted Hormuud SMS API credentials',
    value: {},
    dataType: 'object',
    defaultValue: {},
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: false, // Hidden for security
    shopId: null,
    updatedBy: 'system'
  },

  // Firebase Push Notification Settings
  {
    key: 'firebase_notifications_enabled',
    category: 'notification',
    displayName: 'Enable Firebase Push Notifications',
    description: 'Master switch for mobile app push notifications',
    value: false,
    dataType: 'boolean',
    defaultValue: false,
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'firebase_credentials',
    category: 'notification',
    displayName: 'Firebase Credentials',
    description: 'Encrypted Firebase service account credentials',
    value: {},
    dataType: 'object',
    defaultValue: {},
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: false, // Hidden for security
    shopId: null,
    updatedBy: 'system'
  },

  // Context-specific notification settings
  {
    key: 'notification_contexts_subscription',
    category: 'notification',
    displayName: 'Subscription Notification Contexts',
    description: 'Notification types for subscription events',
    value: ['expiry_reminder', 'renewal_success', 'payment_failed'],
    dataType: 'array',
    defaultValue: ['expiry_reminder', 'renewal_success'],
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'notification_contexts_debt',
    category: 'notification',
    displayName: 'Debt Notification Contexts',
    description: 'Notification types for debt reminder events',
    value: ['debt_reminder', 'payment_overdue', 'debt_settled'],
    dataType: 'array',
    defaultValue: ['debt_reminder', 'payment_overdue'],
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'notification_contexts_welcome',
    category: 'notification',
    displayName: 'Welcome Notification Contexts',
    description: 'Welcome notification types (SuperAdmin only)',
    value: ['user_welcome_sms', 'shop_registration_welcome'],
    dataType: 'array',
    defaultValue: ['user_welcome_sms'],
    accessLevel: 'superAdmin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },

  // Notification timing settings
  {
    key: 'subscription_expiry_reminder_days',
    category: 'notification',
    displayName: 'Subscription Expiry Reminder Days',
    description: 'Days before subscription expiry to send reminder',
    value: 7,
    dataType: 'number',
    defaultValue: 7,
    validation: {
      min: 1,
      max: 30
    },
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  },
  {
    key: 'debt_reminder_interval_days',
    category: 'notification',
    displayName: 'Debt Reminder Interval Days',
    description: 'Days between debt reminder notifications',
    value: 3,
    dataType: 'number',
    defaultValue: 3,
    validation: {
      min: 1,
      max: 14
    },
    accessLevel: 'admin',
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: 'system'
  }
];

/**
 * Helper functions for settings management
 */
const SettingsHelper = {
  /**
   * Initialize system settings
   * @returns {Promise<void>}
   */
  async initializeSystemSettings() {
    try {
      const results = {
        general: await this.ensureGeneralSettings(),
        payment: await this.ensurePaymentSettings(),
        ml: await this.ensureMLSettings(),
        notification: await this.ensureNotificationSettings()
      };
      
      const totalCreated = results.general.created + results.payment.created + results.ml.created + results.notification.created;
      const totalExisting = results.general.existing + results.payment.existing + results.ml.existing + results.notification.existing;
      
      logSuccess(`System settings ready: ${totalCreated} created, ${totalExisting} existing`, 'Settings');
    } catch (error) {
      logError(`Failed to initialize system settings: ${error.message}`, 'Settings', error);
    }
  },

  /**
   * Initialize system settings (optimized for faster startup)
   * @returns {Promise<void>}
   */
  async initializeSystemSettingsOptimized() {
    try {
      // Use bulk operations and parallel execution for faster initialization
      const allDefaultSettings = [
        ...DEFAULT_GENERAL_SETTINGS,
        ...DEFAULT_PAYMENT_SETTINGS,
        ...DEFAULT_ML_SETTINGS,
        ...DEFAULT_NOTIFICATION_SETTINGS
      ];
      
      // Batch check existing settings in one query
      const existingKeys = await Setting.find({
        key: { $in: allDefaultSettings.map(s => s.key) },
        shopId: null
      }, { key: 1 }).lean();
      
      const existingKeySet = new Set(existingKeys.map(s => s.key));
      
      // Filter out settings that already exist
      const settingsToCreate = allDefaultSettings.filter(setting => 
        !existingKeySet.has(setting.key)
      );
      
      let created = 0;
      const existing = allDefaultSettings.length - settingsToCreate.length;
      
      // Bulk insert new settings if any
      if (settingsToCreate.length > 0) {
        try {
          await Setting.insertMany(settingsToCreate, { 
            ordered: false, // Continue on duplicate key errors
            writeConcern: { w: 1, j: false } // Faster write concern for initialization
          });
          created = settingsToCreate.length;
        } catch (error) {
          // Handle partial success in bulk insert
          if (error.writeErrors) {
            created = settingsToCreate.length - error.writeErrors.length;
            logWarning(`Bulk insert had ${error.writeErrors.length} errors, ${created} settings created`, 'Settings');
          } else {
            throw error;
          }
        }
      }
      
      logSuccess(`System settings ready (optimized): ${created} created, ${existing} existing`, 'Settings');
      
      return {
        created,
        existing,
        total: allDefaultSettings.length
      };
    } catch (error) {
      logError(`Failed to initialize system settings (optimized): ${error.message}`, 'Settings', error);
      throw error;
    }
  },

  /**
   * Ensure general settings are in the database
   * @returns {Promise<void>}
   */
  async ensureGeneralSettings() {
    try {
      const results = {
        created: 0,
        existing: 0
      };
      
      for (const setting of DEFAULT_GENERAL_SETTINGS) {
        const exists = await Setting.findOne({
          key: setting.key,
          shopId: setting.shopId
        });
        if (!exists) {
          await Setting.create(setting);
          results.created++;
        } else {
          results.existing++;
        }
      }
      
      return results;
    } catch (error) {
      logError(`Failed to ensure general settings: ${error.message}`, 'Settings', error);
      throw error;
    }
  },

  /**
   * Ensure ML and risk settings are in the database
   * @returns {Promise<void>}
   */
  async ensureMLSettings() {
    try {
      const results = {
        created: 0,
        existing: 0
      };
      for (const setting of DEFAULT_ML_SETTINGS) {
        const exists = await Setting.findOne({
          key: setting.key,
          shopId: setting.shopId
        });
        if (!exists) {
          await Setting.create(setting);
          results.created++;
        } else {
          results.existing++;
        }
      }
      return results;
    } catch (error) {
      logError(`Failed to ensure ML settings: ${error.message}`, 'Settings', error);
      throw error;
    }
  },

  /**
   * Ensure notification settings are in the database
   * @returns {Promise<void>}
   */
  async ensureNotificationSettings() {
    try {
      const results = {
        created: 0,
        existing: 0
      };
      
      for (const setting of DEFAULT_NOTIFICATION_SETTINGS) {
        const exists = await Setting.findOne({
          key: setting.key,
          shopId: setting.shopId
        });
        if (!exists) {
          await Setting.create(setting);
          results.created++;
        } else {
          results.existing++;
        }
      }
      
      return results;
    } catch (error) {
      logError(`Failed to ensure notification settings: ${error.message}`, 'Settings', error);
      throw error;
    }
  },

  /**
   * Ensure payment settings are in the database
   * @returns {Promise<void>}
   */
  async ensurePaymentSettings() {
    try {
      const results = {
        created: 0,
        existing: 0
      };

      for (const setting of DEFAULT_PAYMENT_SETTINGS) {
        // Check if setting already exists
        const exists = await Setting.findOne({
          key: setting.key,
          shopId: setting.shopId
        });

        if (!exists) {
          // Create new setting
          await Setting.create(setting);
          results.created++;
        } else {
          results.existing++;
        }
      }

      return results;
    } catch (error) {
      logError(`Failed to ensure payment settings: ${error.message}`, 'Settings', error);
      throw error;
    }
  },

  /**
   * Create shop-specific settings when a new shop is created
   * @param {string} shopId - ID of the shop
   * @param {string} updatedBy - ID of the user creating the settings
   * @returns {Promise<Object>} Result with counts
   */
  async createShopSettings(shopId, updatedBy = 'system') {
    if (!shopId) {
      throw new Error('Shop ID is required');
    }

    try {
      const shopSettings = [
        // Shop-specific available payment methods
        {
          key: `shop_payment_methods_${shopId}`, // Using a shop-specific key to avoid conflicts
          category: 'payment',
          displayName: 'Shop Payment Methods',
          description: 'Payment methods available for this shop',
          value: ['Cash', 'EVC Plus', 'Bank Transfer', 'Mobile Money', 'offline'],
          dataType: 'array',
          defaultValue: ['Cash', 'EVC Plus', 'offline'],
          accessLevel: 'admin',
          isEditable: true,
          isVisible: true,
          shopId,
          updatedBy
        }
      ];

      const results = {
        created: 0
      };

      for (const setting of shopSettings) {
        // Check if already exists
        const exists = await Setting.findOne({
          key: setting.key,
          shopId
        });

        if (!exists) {
          await Setting.create(setting);
          results.created++;
        }
      }

      logSuccess(`Created ${results.created} shop-specific settings for shop ${shopId}`, 'SettingsHelper');
      return results;
    } catch (error) {
      logError(`Failed to create shop settings for shop ${shopId}: ${error.message}`, 'SettingsHelper', error);
      throw error;
    }
  },

  /**
   * Get all payment methods allowed by the system
   * @returns {Promise<Array<string>>} Array of payment method names
   */
  async getAllowedPaymentMethods() {
    try {
      const setting = await Setting.findOne({
        key: 'payment_methods_available',
        shopId: null
      });

      return setting?.value || ['Cash', 'EVC Plus', 'Bank Transfer', 'offline'];
    } catch (error) {
      logError(`Failed to get allowed payment methods: ${error.message}`, 'SettingsHelper', error);
      return ['Cash', 'EVC Plus', 'Bank Transfer', 'offline'];
    }
  },

  /**
   * Check if a specific payment method is enabled
   * @param {string} methodName - Name of the payment method
   * @returns {Promise<boolean>} Whether the method is enabled
   */
  async isPaymentMethodEnabled(methodName) {
    try {
      // Check if method is in allowed methods
      const allowedMethods = await this.getAllowedPaymentMethods();
      if (!allowedMethods.includes(methodName)) {
        return false;
      }

      // Check global switch for online/offline
      const isOnlineMethod = ['EVC Plus', 'Card', 'Mobile Money'].includes(methodName);
      const isOfflineMethod = ['Cash', 'Bank Transfer', 'Check', 'Other', 'offline'].includes(methodName);

      if (isOnlineMethod) {
        const onlineSetting = await Setting.findOne({
          key: 'enable_online_payment',
          shopId: null
        });
        return onlineSetting?.value !== false; // Default to true if setting doesn't exist
      }

      if (isOfflineMethod) {
        const offlineSetting = await Setting.findOne({
          key: 'enable_offline_payment',
          shopId: null
        });
        return offlineSetting?.value !== false; // Default to true if setting doesn't exist
      }

      return true; // Default to enabled for unknown types
    } catch (error) {
      logError(`Failed to check if payment method is enabled: ${error.message}`, 'SettingsHelper', error);
      return true; // Default to enabled on error
    }
  },

  /**
   * Add or update EVC payment credentials settings
   * @param {Object} credentials - EVC API credentials
   * @param {string} credentials.merchantUId - Merchant UID
   * @param {string} credentials.apiUserId - API User ID
   * @param {string} credentials.apiKey - API Key
   * @param {string} credentials.merchantNo - Merchant Number
   * @param {string} credentials.url - API URL
   * @param {string} shopId - Shop ID (null for global)
   * @param {string} updatedBy - User ID who is updating
   * @param {boolean} isEncrypted - Whether credentials are already encrypted
   * @returns {Promise<Object>} Created/updated setting
   */
  createOrUpdateEVCCredentials: async (credentials, shopId = null, updatedBy = 'system', isEncrypted = false) => {
    try {
      logInfo(`${shopId ? 'Shop' : 'Global'} EVC credentials update requested`, 'SettingsHelper');
      
      // Get validation requirements
      const validationSetting = await Setting.findOne({ 
        key: 'evc_credential_requirements',
        shopId: null // Always use global validation requirements
      });
      
      const validation = validationSetting?.value || {};
      
      // Validate credentials before storing (only for non-encrypted)
      if (!isEncrypted) {
        // Validate each credential against requirements
        Object.entries(credentials).forEach(([key, value]) => {
          const rules = validation[key.toUpperCase()];
          if (rules) {
            if (rules.required && (!value || value.trim() === '')) {
              throw new Error(`${key} is required`);
            }
            
            if (rules.minLength && value.length < rules.minLength) {
              throw new Error(`${key} must be at least ${rules.minLength} characters`);
            }
            
            if (rules.maxLength && value.length > rules.maxLength) {
              throw new Error(`${key} must be at most ${rules.maxLength} characters`);
            }
            
            if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
              throw new Error(`${key} format is invalid`);
            }
          }
        });
      }
      
      // Encrypt credentials with shop-specific key if not already encrypted
      let finalCredentials = credentials;
      if (!isEncrypted) {
        // Use the SecureCredentialService with shop ID for cryptographic isolation
        const SecureCredentialService = require('../../services/secureCredentialService');
        finalCredentials = SecureCredentialService.encryptCredentials(credentials, shopId);
      }
      
      // Store in settings with appropriate key
      return Setting.findOneAndUpdate(
        { key: 'evc_credentials', shopId },
        {
          key: 'evc_credentials',
          category: 'payment',
          displayName: shopId ? `Shop ${shopId} EVC Credentials` : 'Global EVC Credentials',
          description: 'EVC payment gateway credentials',
          value: finalCredentials,
          dataType: 'object',
          defaultValue: {},
          accessLevel: shopId === null ? 'superAdmin' : 'admin',
          isEditable: true,
          isVisible: false,
          shopId,
          updatedBy,
          $push: {
            history: {
              updatedBy,
              updatedAt: new Date(),
              reason: 'EVC credentials update'
            }
          }
        },
        { upsert: true, new: true }
      );
    } catch (error) {
      logError(`Failed to update EVC credentials: ${error.message}`, 'SettingsHelper', error);
      throw error;
    }
  },
  
  /**
   * Get EVC credentials
   * @param {string} shopId - Shop ID (null for global)
   * @returns {Promise<Object|null>} EVC credentials or null if not found
   */
  getEVCCredentials: async (shopId = null) => {
    try {
      const SecureCredentialService = require('../../services/secureCredentialService');
      
      // First try shop-specific credentials
      if (shopId) {
        const shopCredentials = await Setting.findOne({ 
          key: 'evc_credentials', 
          shopId 
        });
        
        if (shopCredentials?.value) {
          // Decrypt using shop-specific key for proper cryptographic isolation
          return SecureCredentialService.decryptCredentials(shopCredentials.value, shopId);
        }
      }
      
      // Fall back to global credentials
      const globalCredentials = await Setting.findOne({ 
        key: 'evc_credentials', 
        shopId: null 
      });
      
      if (globalCredentials?.value) {
        // Decrypt using global key (no shop ID)
        return SecureCredentialService.decryptCredentials(globalCredentials.value, null);
      }
      
      return null;
    } catch (error) {
      logError(`Failed to get EVC credentials: ${error.message}`, 'SettingsHelper', error);
      return null;
    }
  },
  
  /**
   * Check if EVC credentials exist for a shop without actually returning them
   * Used for privacy to show SuperAdmins if a shop has credentials without exposing values
   * @param {string} shopId - Shop ID to check
   * @returns {Promise<Object>} Object with hasCredentials flag and last update timestamp
   */
  hasEVCCredentials: async (shopId) => {
    try {
      if (!shopId) {
        throw new Error('Shop ID is required to check credentials');
      }
      
      const shopCredentials = await Setting.findOne({ 
        key: 'evc_credentials', 
        shopId 
      }, { 'history.updatedAt': 1 });
      
      return {
        hasCredentials: !!shopCredentials?.value,
        lastUpdated: shopCredentials?.history?.length > 0 
          ? shopCredentials.history[shopCredentials.history.length - 1].updatedAt 
          : null
      };
    } catch (error) {
      logError(`Failed to check EVC credentials existence: ${error.message}`, 'SettingsHelper', error);
      return { hasCredentials: false, lastUpdated: null };
    }
  },

  /**
   * Create or update SMS credentials (Hormuud provider)
   * @param {Object} credentials - SMS API credentials
   * @param {string} credentials.username - SMS username
   * @param {string} credentials.password - SMS password
   * @param {string} shopId - Shop ID (null for global)
   * @param {string} updatedBy - User ID who is updating
   * @param {boolean} isEncrypted - Whether credentials are already encrypted
   * @returns {Promise<Object>} Created/updated setting
   */
  createOrUpdateSMSCredentials: async (credentials, shopId = null, updatedBy = 'system', isEncrypted = false) => {
    try {
      logInfo(`${shopId ? 'Shop' : 'Global'} SMS credentials update requested`, 'SettingsHelper');
      
      // Validate credentials before storing (only for non-encrypted)
      if (!isEncrypted) {
        if (!credentials.username || credentials.username.trim() === '') {
          throw new Error('SMS username is required');
        }
        if (!credentials.password || credentials.password.trim() === '') {
          throw new Error('SMS password is required');
        }
      }
      
      // Encrypt credentials if not already encrypted
      let finalCredentials = credentials;
      if (!isEncrypted) {
        const SecureCredentialService = require('../../services/secureCredentialService');
        finalCredentials = SecureCredentialService.encryptCredentials(credentials, shopId);
      }
      
      // Store in settings
      return Setting.findOneAndUpdate(
        { key: 'sms_credentials', shopId },
        {
          key: 'sms_credentials',
          category: 'notification',
          displayName: shopId ? `Shop ${shopId} SMS Credentials` : 'Global SMS Credentials',
          description: 'Hormuud SMS API credentials',
          value: finalCredentials,
          dataType: 'object',
          defaultValue: {},
          accessLevel: shopId === null ? 'superAdmin' : 'admin',
          isEditable: true,
          isVisible: false,
          shopId,
          updatedBy,
          $push: {
            history: {
              updatedBy,
              updatedAt: new Date(),
              reason: 'SMS credentials update'
            }
          }
        },
        { upsert: true, new: true }
      );
    } catch (error) {
      logError(`Failed to update SMS credentials: ${error.message}`, 'SettingsHelper', error);
      throw error;
    }
  },

  /**
   * Create or update Firebase credentials
   * @param {Object} credentials - Firebase service account credentials
   * @param {string} shopId - Shop ID (null for global)
   * @param {string} updatedBy - User ID who is updating
   * @param {boolean} isEncrypted - Whether credentials are already encrypted
   * @returns {Promise<Object>} Created/updated setting
   */
  createOrUpdateFirebaseCredentials: async (credentials, shopId = null, updatedBy = 'system', isEncrypted = false) => {
    try {
      logInfo(`${shopId ? 'Shop' : 'Global'} Firebase credentials update requested`, 'SettingsHelper');
      
      // Validate credentials before storing (only for non-encrypted)
      if (!isEncrypted) {
        if (!credentials.project_id) {
          throw new Error('Firebase project_id is required');
        }
        if (!credentials.private_key) {
          throw new Error('Firebase private_key is required');
        }
        if (!credentials.client_email) {
          throw new Error('Firebase client_email is required');
        }
      }
      
      // Encrypt credentials if not already encrypted
      let finalCredentials = credentials;
      if (!isEncrypted) {
        const SecureCredentialService = require('../../services/secureCredentialService');
        finalCredentials = SecureCredentialService.encryptCredentials(credentials, shopId);
      }
      
      // Store in settings
      return Setting.findOneAndUpdate(
        { key: 'firebase_credentials', shopId },
        {
          key: 'firebase_credentials',
          category: 'notification',
          displayName: shopId ? `Shop ${shopId} Firebase Credentials` : 'Global Firebase Credentials',
          description: 'Firebase push notification service account credentials',
          value: finalCredentials,
          dataType: 'object',
          defaultValue: {},
          accessLevel: 'superAdmin',
          isEditable: true,
          isVisible: false,
          shopId,
          updatedBy,
          $push: {
            history: {
              updatedBy,
              updatedAt: new Date(),
              reason: 'Firebase credentials update'
            }
          }
        },
        { upsert: true, new: true }
      );
    } catch (error) {
      logError(`Failed to update Firebase credentials: ${error.message}`, 'SettingsHelper', error);
      throw error;
    }
  },

  /**
   * Get SMS credentials
   * @param {string} shopId - Shop ID (null for global)
   * @returns {Promise<Object|null>} SMS credentials or null if not found
   */
  getSMSCredentials: async (shopId = null) => {
    try {
      const SecureCredentialService = require('../../services/secureCredentialService');
      
      // First try shop-specific credentials
      if (shopId) {
        const shopCredentials = await Setting.findOne({ 
          key: 'sms_credentials', 
          shopId 
        });
        
        if (shopCredentials?.value) {
          return SecureCredentialService.decryptCredentials(shopCredentials.value, shopId);
        }
      }
      
      // Fall back to global credentials
      const globalCredentials = await Setting.findOne({ 
        key: 'sms_credentials', 
        shopId: null 
      });
      
      if (globalCredentials?.value) {
        return SecureCredentialService.decryptCredentials(globalCredentials.value, null);
      }
      
      return null;
    } catch (error) {
      logError(`Failed to get SMS credentials: ${error.message}`, 'SettingsHelper', error);
      return null;
    }
  },

  /**
   * Get Firebase credentials from environment variables or database
   * @param {string} shopId - Shop ID (null for global)
   * @returns {Promise<Object|null>} Firebase credentials or null if not found
   */
  getFirebaseCredentials: async (shopId = null) => {
    try {
      // Method 1: From environment variable (JSON string)
      if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        logInfo('Loading Firebase credentials from environment variable', 'SettingsHelper');
        const serviceAccountKey = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
        return {
          serviceAccountKey: serviceAccountKey,
          projectId: process.env.FIREBASE_PROJECT_ID || serviceAccountKey.project_id
        };
      }
      
      // Method 2: From file path
      if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        logInfo('Loading Firebase credentials from file', 'SettingsHelper');
        const fs = require('fs');
        const filePath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
        
        if (!fs.existsSync(filePath)) {
          throw new Error(`Firebase service account file not found: ${filePath}`);
        }
        
        const serviceAccountKey = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        return {
          serviceAccountKey: serviceAccountKey,
          projectId: process.env.FIREBASE_PROJECT_ID || serviceAccountKey.project_id
        };
      }
      
      // Method 3: From database settings (fallback)
      const SecureCredentialService = require('../../services/secureCredentialService');
      
      // First try shop-specific credentials
      if (shopId) {
        const shopCredentials = await Setting.findOne({ 
          key: 'firebase_credentials', 
          shopId 
        });
        
        if (shopCredentials?.value) {
          return SecureCredentialService.decryptCredentials(shopCredentials.value, shopId);
        }
      }
      
      // Fall back to global credentials in database
      const globalCredentials = await Setting.findOne({ 
        key: 'firebase_credentials', 
        shopId: null 
      });
      
      if (globalCredentials?.value) {
        return SecureCredentialService.decryptCredentials(globalCredentials.value, null);
      }
      
      throw new Error('No Firebase credentials found in environment or database. Set either GOOGLE_APPLICATION_CREDENTIALS or FIREBASE_SERVICE_ACCOUNT_KEY');
    } catch (error) {
      logError(`Failed to get Firebase credentials: ${error.message}`, 'SettingsHelper', error);
      throw error;
    }
  },
  
  /**
   * Update a setting value with authorization checks.
   * @param {string} key - Setting key
   * @param {string|null} shopId - Shop ID for shop-specific settings, null for global
   * @param {any} value - New setting value
   * @param {string} updatedBy - User ID who made the update
   * @param {string} userRole - Role of the user making the update
   * @param {string} [reason='Manual update'] - Reason for the update (for audit trail)
   * @returns {Promise<Object>} Updated setting document
   */
  updateSetting: async (key, shopId, value, updatedBy, userRole, reason = 'Manual update') => {
    try {
      if (!key) {
        throw new AppError('Setting key is required', 400, 'missing_setting_key');
      }

      logInfo(`Attempting to update setting '${key}' for ${shopId ? 'shop ' + shopId : 'global'} by user ${updatedBy} with role ${userRole}`, 'SettingsHelper');

      // Find existing setting first to check access level and if it exists
      const existingSetting = await Setting.findOne({ key, shopId });

      let settingAccessLevel = null; // Default access level if creating a new setting

      if (existingSetting) {
        settingAccessLevel = existingSetting.accessLevel;

        // --- Authorization Check ---
        let isAuthorized = false;

        if (userRole === 'superAdmin') {
          isAuthorized = true; // SuperAdmins can update anything
        } else if (userRole === 'admin') {
          // Admins can only update settings with accessLevel 'admin' or 'all'
          // And only for their assigned shop (controller already enforces shopId matching, but this is a safeguard)
          if (existingSetting.shopId === shopId) { // Ensure the found setting matches the requested shopId (important for Admins)
            if (settingAccessLevel === 'admin' || settingAccessLevel === 'all') {
               isAuthorized = true;
            }
          }
        } else if (userRole === 'all') {
           // Users with 'all' access level can only update settings with accessLevel 'all'
           if (settingAccessLevel === 'all'){
             isAuthorized = true;
           }
        }

        if (!isAuthorized) {
           throw new AppError('You do not have permission to update this setting', 403, 'unauthorized_setting_update');
        }
        // --- End Authorization Check ---

        // Proceed with update if authorized
        // Update existing setting
        const updated = await Setting.findOneAndUpdate(
          { key, shopId },
          { 
            $set: { value },
            $push: {
              history: {
                updatedBy,
                updatedAt: new Date(),
                reason,
                previousValue: existingSetting.value
              }
            }
          },
          { new: true }
        );

        logSuccess(`Updated setting '${key}' for ${shopId ? 'shop ' + shopId : 'global'} successfully`, 'SettingsHelper');
        return updated;

      } else {
        // If the setting doesn't exist, we allow creation, but check if the user role
        // is authorized to create a setting at this scope/with this implicit access level.
        // Based on PRD, default accessLevel for new shop settings is 'admin', global is 'superAdmin'.
        let canCreate = false;
        let newSettingAccessLevel = shopId ? 'admin' : 'superAdmin'; // Determine implicit access level for the new setting

        if (userRole === 'superAdmin') {
          canCreate = true; // SuperAdmins can create global or shop-specific settings
        } else if (userRole === 'admin' && shopId) {
           // Admins can only create shop-specific settings (where the new setting will have accessLevel 'admin')
           canCreate = true;
        } // Other roles cannot create settings via this generic update endpoint

        if (!canCreate) {
           throw new AppError('You do not have permission to create a setting at this scope', 403, 'unauthorized_setting_creation');
        }

        // Create new setting if it doesn't exist and user is authorized to create
        const newSetting = {
          key,
          value,
          category: key.split('_')[0] || 'general', // Derive category from key, fallback to 'general'
          // Attempt to derive displayName and description if possible, otherwise use a default
          displayName: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          description: `Setting for ${key}`, // Default description
          dataType: typeof value, // Infer dataType from value (consider adding explicit type in body for robustness)
          defaultValue: value, // Set defaultValue to the initial value
          accessLevel: newSettingAccessLevel, // Set the determined access level
          isEditable: true, // New settings are typically editable by default
          isVisible: true, // New settings are typically visible by default
          shopId,
          updatedBy,
          history: [{
            updatedBy,
            updatedAt: new Date(),
            reason
          }]
        };

        // Add basic validation inferred from dataType if no specific validation is provided
        // This might need refinement based on your Setting model's validation structure
        if (newSetting.dataType === 'number') newSetting.validation = { min: null, max: null };
        if (newSetting.dataType === 'string') newSetting.validation = { minLength: null, maxLength: null, pattern: null };
        if (newSetting.dataType === 'array') newSetting.validation = { minItems: null, maxItems: null };
        if (newSetting.dataType === 'object') newSetting.validation = { required: [] };

        const created = await Setting.create(newSetting);
        logSuccess(`Created new setting '${key}' for ${shopId ? 'shop ' + shopId : 'global'}`, 'SettingsHelper');
        return created;
      }

    } catch (error) {
      logError(`Failed to update setting ${key}: ${error.message}`, 'SettingsHelper', error);
       // Re-throw AppError as is, wrap others
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('Failed to update setting', 500, 'setting_update_error');
    }
  },

  /**
   * Get Firebase credentials for Admin SDK
   * @returns {Object|null} Firebase credentials
   */
  getFirebaseCredentials: () => {
    try {
      // Method 1: Use file path credentials
      if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        const fs = require('fs');
        const path = require('path');
        
        // Try absolute path first
        let credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
        
        // If not absolute, try relative to project root
        if (!fs.existsSync(credentialsPath)) {
          credentialsPath = path.resolve(process.cwd(), process.env.GOOGLE_APPLICATION_CREDENTIALS);
        }
        
        // Try default location if still not found
        if (!fs.existsSync(credentialsPath)) {
          credentialsPath = path.resolve(process.cwd(), 'deyncare-47d99-firebase-adminsdk-fbsvc-cea556463f.json');
        }
        
        if (fs.existsSync(credentialsPath)) {
          const serviceAccount = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
          return {
            projectId: serviceAccount.project_id || process.env.FIREBASE_PROJECT_ID,
            serviceAccountKey: serviceAccount
          };
        }
      }
      
      // Method 2: Use inline JSON credentials
      if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
        return {
          projectId: serviceAccount.project_id || process.env.FIREBASE_PROJECT_ID,
          serviceAccountKey: serviceAccount
        };
      }
      
      // Method 3: Try default file location
      const fs = require('fs');
      const path = require('path');
      const defaultPath = path.resolve(process.cwd(), 'deyncare-47d99-firebase-adminsdk-fbsvc-cea556463f.json');
      
      if (fs.existsSync(defaultPath)) {
        const serviceAccount = JSON.parse(fs.readFileSync(defaultPath, 'utf8'));
        return {
          projectId: serviceAccount.project_id || process.env.FIREBASE_PROJECT_ID,
          serviceAccountKey: serviceAccount
        };
      }
      
      console.error('Firebase credentials not found. Please set GOOGLE_APPLICATION_CREDENTIALS or FIREBASE_SERVICE_ACCOUNT_KEY');
      return null;
    } catch (error) {
      console.error('Error loading Firebase credentials:', error.message);
      return null;
    }
  },

  /**
   * Get Firebase configuration for client-side
   * @returns {Object} Client Firebase config
   */
  getFirebaseClientConfig: () => {
    const config = {
      apiKey: process.env.FIREBASE_WEB_API_KEY,
      authDomain: `${process.env.FIREBASE_PROJECT_ID}.firebaseapp.com`,
      projectId: process.env.FIREBASE_PROJECT_ID,
      storageBucket: `${process.env.FIREBASE_PROJECT_ID}.appspot.com`,
      messagingSenderId: process.env.FIREBASE_PROJECT_NUMBER,
      appId: process.env.FIREBASE_APP_ID // Optional
    };

    // Validate required fields
    const requiredFields = ['apiKey', 'projectId', 'messagingSenderId'];
    const missingFields = requiredFields.filter(field => !config[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`Missing required Firebase client config fields: ${missingFields.join(', ')}`);
    }

    return config;
  },

  /**
   * Validate Firebase configuration
   * @returns {Object} Validation result with details
   */
  validateFirebaseConfig: () => {
    const result = {
      valid: true,
      errors: [],
      warnings: [],
      config: {}
    };

    // Check required environment variables
    const required = [
      'FIREBASE_PROJECT_ID',
      'FIREBASE_WEB_API_KEY',
      'FIREBASE_PROJECT_NUMBER'
    ];

    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      result.valid = false;
      result.errors.push(`Missing required Firebase environment variables: ${missing.join(', ')}`);
    }

    // Check if we have service account credentials
    const hasFileCredentials = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    const hasInlineCredentials = process.env.FIREBASE_SERVICE_ACCOUNT_KEY;
    
    if (!hasFileCredentials && !hasInlineCredentials) {
      result.valid = false;
      result.errors.push('Missing Firebase service account credentials. Set either GOOGLE_APPLICATION_CREDENTIALS or FIREBASE_SERVICE_ACCOUNT_KEY');
    }

    // Validate file credentials if specified
    if (hasFileCredentials) {
      const fs = require('fs');
      if (!fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS)) {
        result.valid = false;
        result.errors.push(`Firebase service account file not found: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`);
      } else {
        try {
          const serviceAccount = JSON.parse(fs.readFileSync(process.env.GOOGLE_APPLICATION_CREDENTIALS, 'utf8'));
          if (serviceAccount.type !== 'service_account') {
            result.valid = false;
            result.errors.push('Invalid service account file: not a service account');
          }
        } catch (error) {
          result.valid = false;
          result.errors.push(`Invalid service account file: ${error.message}`);
        }
      }
    }

    // Validate inline credentials if specified
    if (hasInlineCredentials) {
      try {
        const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
        if (serviceAccount.type !== 'service_account') {
          result.valid = false;
          result.errors.push('Invalid inline service account: not a service account');
        }
      } catch (error) {
        result.valid = false;
        result.errors.push(`Invalid inline service account JSON: ${error.message}`);
      }
    }

    // Check optional settings
    const pushEnabled = process.env.PUSH_NOTIFICATIONS_ENABLED === 'true';
    const remindersEnabled = process.env.DEBT_REMINDERS_ENABLED === 'true';
    
    if (!pushEnabled) {
      result.warnings.push('Push notifications are disabled');
    }
    
    if (!remindersEnabled) {
      result.warnings.push('Debt reminders are disabled');
    }

    // Store config for reference
    result.config = {
      projectId: process.env.FIREBASE_PROJECT_ID,
      pushNotificationsEnabled: pushEnabled,
      debtRemindersEnabled: remindersEnabled,
      batchSize: parseInt(process.env.NOTIFICATION_BATCH_SIZE) || 500,
      timeout: parseInt(process.env.NOTIFICATION_TIMEOUT_SECONDS) || 35,
      credentialsMethod: hasFileCredentials ? 'File Path' : hasInlineCredentials ? 'Inline JSON' : 'None'
    };

    return result;
  },

  /**
   * Get notification system settings
   * @returns {Object} Notification settings
   */
  getNotificationSettings: () => {
    return {
      pushNotificationsEnabled: process.env.PUSH_NOTIFICATIONS_ENABLED === 'true',
      debtRemindersEnabled: process.env.DEBT_REMINDERS_ENABLED === 'true',
      batchSize: parseInt(process.env.NOTIFICATION_BATCH_SIZE) || 500,
      retryAttempts: parseInt(process.env.NOTIFICATION_RETRY_ATTEMPTS) || 3,
      timeoutSeconds: parseInt(process.env.NOTIFICATION_TIMEOUT_SECONDS) || 35,
      
      // Development settings
      emulatorEnabled: process.env.FIREBASE_EMULATOR_ENABLED === 'true',
      emulatorHost: process.env.FIREBASE_EMULATOR_HOST || 'localhost',
      emulatorPort: parseInt(process.env.FIREBASE_EMULATOR_PORT) || 9099,
      
      // Security settings
      secureMode: process.env.NODE_ENV === 'production',
      monitoringEnabled: process.env.FIREBASE_MONITORING_ENABLED === 'true',
      analyticsEnabled: process.env.NOTIFICATION_ANALYTICS_ENABLED === 'true'
    };
  }
};

module.exports = SettingsHelper;
