/**
 * SuperAdmin Payment Transaction Service
 * Handles all business logic for payment transaction management
 */
const { Payment, Shop, Subscription } = require('../../models');
const { AppError, logInfo, logError, logSuccess } = require('../../utils');
const EmailService = require('../emailService');

/**
 * Get payment transactions with filtering and pagination
 * @param {Object} options - Filter and pagination options
 * @returns {Promise<Object>} Payment transactions with pagination
 */
const getPaymentTransactions = async (options = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      method,
      startDate,
      endDate,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options;

    // Build query for subscription payments only
    const query = {
      paymentContext: 'subscription',
      isDeleted: false
    };

    // Add filters
    if (status) {
      query.status = status;
    }

    if (method) {
      query.method = method;
    }

    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    if (search) {
      query.$or = [
        { customerName: { $regex: search, $options: 'i' } },
        { shopName: { $regex: search, $options: 'i' } },
        { paymentId: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Get total count for pagination
    const totalCount = await Payment.countDocuments(query);

    // Get payments with pagination
    const payments = await Payment.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);

    logInfo(`Retrieved ${payments.length} payment transactions out of ${totalCount} total`, 'PaymentTransactionService');

    return {
      payments,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    logError(`Error getting payment transactions: ${error.message}`, 'PaymentTransactionService', error);
    throw new AppError('Failed to retrieve payment transactions', 500, 'payment_retrieval_error');
  }
};

/**
 * Get single payment transaction by ID
 * @param {string} paymentId - Payment ID
 * @returns {Promise<Object>} Payment transaction
 */
const getPaymentTransactionById = async (paymentId) => {
  try {
    const payment = await Payment.findOne({
      paymentId,
      paymentContext: 'subscription',
      isDeleted: false
    }).lean();

    if (!payment) {
      throw new AppError('Payment transaction not found', 404, 'payment_not_found');
    }

    // Get related shop and subscription information
    const [shop, subscription] = await Promise.all([
      Shop.findOne({ shopId: payment.shopId }).lean(),
      Subscription.findOne({ subscriptionId: payment.subscriptionId }).lean()
    ]);

    const paymentWithDetails = {
      ...payment,
      shop: shop ? {
        shopId: shop.shopId,
        shopName: shop.shopName,
        status: shop.status
      } : null,
      subscription: subscription ? {
        subscriptionId: subscription.subscriptionId,
        status: subscription.status,
        planType: subscription.plan?.type,
        endDate: subscription.dates?.endDate
      } : null
    };

    logInfo(`Retrieved payment transaction: ${paymentId}`, 'PaymentTransactionService');

    return paymentWithDetails;
  } catch (error) {
    logError(`Error getting payment transaction ${paymentId}: ${error.message}`, 'PaymentTransactionService', error);
    
    if (error instanceof AppError) {
      throw error;
    }
    
    throw new AppError('Failed to retrieve payment transaction', 500, 'payment_retrieval_error');
  }
};

/**
 * Approve payment transaction
 * @param {string} paymentId - Payment ID
 * @param {string} approvedBy - SuperAdmin user ID
 * @param {string} approvalNotes - Approval notes
 * @param {boolean} activateSubscription - Whether to activate subscription
 * @returns {Promise<Object>} Updated payment transaction
 */
const approvePayment = async (paymentId, approvedBy, approvalNotes = '', activateSubscription = true) => {
  try {
    const payment = await Payment.findOne({
      paymentId,
      paymentContext: 'subscription',
      isDeleted: false
    });

    if (!payment) {
      throw new AppError('Payment transaction not found', 404, 'payment_not_found');
    }

    if (payment.status === 'approved') {
      throw new AppError('Payment is already approved', 400, 'payment_already_approved');
    }

    // Approve the payment
    await payment.approvePayment(approvedBy, approvalNotes);

    // Update subscription if needed
    let subscriptionStatus = null;
    if (activateSubscription && payment.subscriptionId) {
      const subscription = await Subscription.findOne({ subscriptionId: payment.subscriptionId });
      
      if (subscription) {
        subscription.status = 'active';
        subscription.payment.verified = true;
        subscription.payment.lastPaymentDate = new Date();
        
        // Add to subscription history
        subscription.history.push({
          action: 'payment_approved',
          date: new Date(),
          performedBy: approvedBy,
          details: {
            paymentId: payment.paymentId,
            amount: payment.amount,
            approvalNotes
          }
        });

        await subscription.save();
        subscriptionStatus = 'active';

        logInfo(`Subscription ${payment.subscriptionId} activated after payment approval`, 'PaymentTransactionService');
      }
    }

    // Update shop status if needed
    if (activateSubscription && payment.shopId) {
      const shop = await Shop.findOne({ shopId: payment.shopId });
      
      if (shop) {
        shop.access.isPaid = true;
        shop.access.isActivated = true;
        shop.status = 'active';
        await shop.save();

        logInfo(`Shop ${payment.shopId} activated after payment approval`, 'PaymentTransactionService');
      }
    }

    // Send approval notification email
    try {
      if (payment.customerName) {
        await EmailService.sendPaymentApprovalNotification({
          customerName: payment.customerName,
          paymentId: payment.paymentId,
          amount: payment.amount,
          shopName: payment.shopName,
          approvalNotes
        });
      }
    } catch (emailError) {
      logError(`Failed to send approval notification email: ${emailError.message}`, 'PaymentTransactionService', emailError);
      // Don't fail the approval process if email fails
    }

    logSuccess(`Payment ${paymentId} approved by ${approvedBy}`, 'PaymentTransactionService');

    return {
      paymentId: payment.paymentId,
      status: payment.status,
      approvedAt: payment.approvedAt,
      approvedBy: payment.approvedBy,
      subscriptionStatus
    };
  } catch (error) {
    logError(`Error approving payment ${paymentId}: ${error.message}`, 'PaymentTransactionService', error);
    
    if (error instanceof AppError) {
      throw error;
    }
    
    throw new AppError('Failed to approve payment', 500, 'payment_approval_error');
  }
};

/**
 * Reject payment transaction
 * @param {string} paymentId - Payment ID
 * @param {string} rejectedBy - SuperAdmin user ID
 * @param {string} rejectionReason - Reason for rejection
 * @param {string} rejectionNotes - Rejection notes
 * @returns {Promise<Object>} Updated payment transaction
 */
const rejectPayment = async (paymentId, rejectedBy, rejectionReason, rejectionNotes = '') => {
  try {
    const payment = await Payment.findOne({
      paymentId,
      paymentContext: 'subscription',
      isDeleted: false
    });

    if (!payment) {
      throw new AppError('Payment transaction not found', 404, 'payment_not_found');
    }

    if (payment.status === 'rejected') {
      throw new AppError('Payment is already rejected', 400, 'payment_already_rejected');
    }

    // Reject the payment
    await payment.rejectPayment(rejectedBy, `${rejectionReason}: ${rejectionNotes}`);

    // Send rejection notification email
    try {
      if (payment.customerName) {
        await EmailService.sendPaymentRejectionNotification({
          customerName: payment.customerName,
          paymentId: payment.paymentId,
          amount: payment.amount,
          shopName: payment.shopName,
          rejectionReason,
          rejectionNotes
        });
      }
    } catch (emailError) {
      logError(`Failed to send rejection notification email: ${emailError.message}`, 'PaymentTransactionService', emailError);
      // Don't fail the rejection process if email fails
    }

    logInfo(`Payment ${paymentId} rejected by ${rejectedBy}`, 'PaymentTransactionService');

    return {
      paymentId: payment.paymentId,
      status: payment.status,
      rejectedAt: payment.approvedAt, // Using approvedAt field for rejection timestamp
      rejectedBy: payment.approvedBy, // Using approvedBy field for rejection user
      rejectionReason
    };
  } catch (error) {
    logError(`Error rejecting payment ${paymentId}: ${error.message}`, 'PaymentTransactionService', error);
    
    if (error instanceof AppError) {
      throw error;
    }
    
    throw new AppError('Failed to reject payment', 500, 'payment_rejection_error');
  }
};

/**
 * Export payment data
 * @param {Object} options - Export options
 * @returns {Promise<Object>} Export data
 */
const exportPaymentData = async (options = {}) => {
  try {
    const {
      format = 'csv',
      status,
      method,
      startDate,
      endDate,
      search
    } = options;

    // Get all payments for export (no pagination)
    const query = {
      paymentContext: 'subscription',
      isDeleted: false
    };

    if (status) query.status = status;
    if (method) query.method = method;
    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }
    if (search) {
      query.$or = [
        { customerName: { $regex: search, $options: 'i' } },
        { shopName: { $regex: search, $options: 'i' } },
        { paymentId: { $regex: search, $options: 'i' } }
      ];
    }

    const payments = await Payment.find(query).sort({ createdAt: -1 }).lean();

    if (format === 'csv') {
      const csvData = generateCSVData(payments);
      return {
        data: csvData,
        contentType: 'text/csv'
      };
    } else if (format === 'pdf') {
      const pdfData = await generatePDFData(payments);
      return {
        data: pdfData,
        contentType: 'application/pdf'
      };
    }

    throw new AppError('Unsupported export format', 400, 'unsupported_format');
  } catch (error) {
    logError(`Error exporting payment data: ${error.message}`, 'PaymentTransactionService', error);
    
    if (error instanceof AppError) {
      throw error;
    }
    
    throw new AppError('Failed to export payment data', 500, 'export_error');
  }
};

/**
 * Get payment statistics
 * @param {Object} options - Statistics options
 * @returns {Promise<Object>} Payment statistics
 */
const getPaymentStatistics = async (options = {}) => {
  try {
    const { startDate, endDate } = options;

    const query = {
      paymentContext: 'subscription',
      isDeleted: false
    };

    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Get total payments
    const totalPayments = await Payment.countDocuments(query);

    // Get payments by status
    const statusStats = await Payment.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Get payments by method
    const methodStats = await Payment.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$method',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Calculate success rate
    const successfulPayments = await Payment.countDocuments({
      ...query,
      status: { $in: ['success', 'approved'] }
    });

    const successRate = totalPayments > 0 ? (successfulPayments / totalPayments) * 100 : 0;

    // Calculate total revenue
    const revenueStats = await Payment.aggregate([
      { $match: { ...query, status: { $in: ['success', 'approved'] } } },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          averageAmount: { $avg: '$amount' }
        }
      }
    ]);

    const totalRevenue = revenueStats.length > 0 ? revenueStats[0].totalRevenue : 0;
    const averageAmount = revenueStats.length > 0 ? revenueStats[0].averageAmount : 0;

    logInfo(`Generated payment statistics for ${totalPayments} payments`, 'PaymentTransactionService');

    return {
      totalPayments,
      successRate: Math.round(successRate * 100) / 100,
      totalRevenue,
      averageAmount: Math.round(averageAmount * 100) / 100,
      statusDistribution: statusStats,
      methodDistribution: methodStats
    };
  } catch (error) {
    logError(`Error getting payment statistics: ${error.message}`, 'PaymentTransactionService', error);
    throw new AppError('Failed to get payment statistics', 500, 'statistics_error');
  }
};

/**
 * Generate CSV data for export
 * @param {Array} payments - Payment data
 * @returns {string} CSV data
 */
const generateCSVData = (payments) => {
  const headers = [
    'Payment ID',
    'Customer Name',
    'Shop Name',
    'Amount',
    'Payment Method',
    'Status',
    'Created At',
    'Approved At',
    'Approved By'
  ];

  const rows = payments.map(payment => [
    payment.paymentId,
    payment.customerName || 'N/A',
    payment.shopName || 'N/A',
    payment.amount,
    payment.method,
    payment.status,
    payment.createdAt ? new Date(payment.createdAt).toISOString() : 'N/A',
    payment.approvedAt ? new Date(payment.approvedAt).toISOString() : 'N/A',
    payment.approvedBy || 'N/A'
  ]);

  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n');

  return csvContent;
};

/**
 * Generate PDF data for export
 * @param {Array} payments - Payment data
 * @returns {Promise<Buffer>} PDF data
 */
const generatePDFData = async (payments) => {
  // This is a placeholder for PDF generation
  // In a real implementation, you would use a library like PDFKit or Puppeteer
  const pdfContent = `Payment Transactions Report\n\nGenerated on: ${new Date().toISOString()}\n\n`;
  
  payments.forEach(payment => {
    pdfContent += `Payment ID: ${payment.paymentId}\n`;
    pdfContent += `Customer: ${payment.customerName || 'N/A'}\n`;
    pdfContent += `Shop: ${payment.shopName || 'N/A'}\n`;
    pdfContent += `Amount: $${payment.amount}\n`;
    pdfContent += `Status: ${payment.status}\n\n`;
  });

  return Buffer.from(pdfContent, 'utf8');
};

module.exports = {
  getPaymentTransactions,
  getPaymentTransactionById,
  approvePayment,
  rejectPayment,
  exportPaymentData,
  getPaymentStatistics
}; 