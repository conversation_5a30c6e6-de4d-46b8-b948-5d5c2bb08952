/**
 * DeynCare API Bridge
 * 
 * This module bridges the gap between your frontend components and backend API.
 * It uses the API contract definitions and works with your existing module structure
 * to provide consistent API interactions across the application.
 * 
 * Benefits:
 * 1. Standardized endpoint usage
 * 2. Consistent error handling
 * 3. Response format normalization
 * 4. Automatic cache management
 * 5. Integrated token management
 */
import { toast } from 'sonner';
import api from './index';
import { ENDPOINTS, responseFormatters, errorHandlers } from './contract';
import { getAccessToken, setTokens, clearTokens, refreshToken } from './token';

// Import existing API modules
import authAPI from './modules/auth';
import shopAPI from './modules/shop';
import userAPI from './modules/user';
import notificationAPI from './modules/notification';

// Cache store for API responses
const apiCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
const MAX_CACHE_SIZE = 100; // Maximum number of cache entries

// PERFORMANCE FIX: Periodic cache cleanup to prevent memory leaks
let cacheCleanupInterval;
const startCacheCleanup = () => {
  if (cacheCleanupInterval) return; // Already started

  cacheCleanupInterval = setInterval(() => {
    const now = Date.now();
    let expiredCount = 0;

    // Remove expired entries
    apiCache.forEach((value, key) => {
      if (value.expiry <= now) {
        apiCache.delete(key);
        expiredCount++;
      }
    });

    // If cache is still too large, remove oldest entries
    if (apiCache.size > MAX_CACHE_SIZE) {
      const entries = Array.from(apiCache.entries());
      entries.sort((a, b) => a[1].expiry - b[1].expiry);

      const toRemove = apiCache.size - MAX_CACHE_SIZE;
      for (let i = 0; i < toRemove; i++) {
        apiCache.delete(entries[i][0]);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      console.log(`[API Bridge] Cache cleanup: removed ${expiredCount} entries, ${apiCache.size} remaining`);
    }
  }, 60000); // Run every minute
};

// Start cleanup when module loads
if (typeof window !== 'undefined') {
  startCacheCleanup();
}

/**
 * Helper function to generate cache key
 */
const generateCacheKey = (url, params) => {
  const queryString = params ? JSON.stringify(params) : '';
  return `${url}_${queryString}`;
};

/**
 * DeynCare API Bridge
 * Provides consistent methods for interacting with the backend API
 */
const apiBridge = {
  /**
   * Token management methods
   */
  token: {
    /**
     * Get the current access token
     * @returns {string|null} Current access token or null
     */
    getToken: () => getAccessToken(),
    
    /**
     * Set authentication tokens
     * @param {string} accessToken - JWT access token
     * @param {string} refreshToken - JWT refresh token
     */
    setTokens: (accessToken, refreshToken) => setTokens(accessToken, refreshToken),
    
    /**
     * Clear all authentication tokens (logout)
     */
    clearTokens: () => clearTokens(),
    
    /**
     * Refresh the authentication token
     * @returns {Promise<string|null>} New token or null
     */
    refreshToken: () => refreshToken()
  },
  
  /**
   * Clear the entire API cache or specific cache entries
   * @param {string|array} keys - Optional specific cache key(s) or endpoint patterns to clear
   */
  clearCache: (keys = null) => {
    if (keys) {
      const keyArray = Array.isArray(keys) ? keys : [keys];
      let totalCleared = 0;

      keyArray.forEach(keyPattern => {
        const keysToDelete = [];

        if (keyPattern.includes('*')) {
          // Handle wildcard patterns
          const basePattern = keyPattern.replace('*', '');
          apiCache.forEach((_, key) => {
            if (key.startsWith(basePattern)) {
              keysToDelete.push(key);
            }
          });
        } else {
          // Handle exact key matches
          if (apiCache.has(keyPattern)) {
            keysToDelete.push(keyPattern);
          }
        }

        keysToDelete.forEach(key => {
          apiCache.delete(key);
          totalCleared++;
        });
        console.log(`[API Bridge] Cleared cache for pattern: ${keyPattern} (${keysToDelete.length} entries)`);
      });

      // PERFORMANCE FIX: Force garbage collection hint for large cache clears
      if (totalCleared > 10 && typeof window !== 'undefined' && window.gc) {
        setTimeout(() => window.gc(), 100);
      }
    } else {
      // Clear all cache
      const cacheSize = apiCache.size;
      apiCache.clear();
      console.log(`[API Bridge] Cleared entire API cache (${cacheSize} entries)`);

      // PERFORMANCE FIX: Force garbage collection hint for full cache clear
      if (cacheSize > 20 && typeof window !== 'undefined' && window.gc) {
        setTimeout(() => window.gc(), 100);
      }
    }
  },
  
  /**
   * Make a GET request with caching
   * @param {string} url - API endpoint URL
   * @param {Object} options - Request options
   * @returns {Promise} - API response
   */
  get: async (url, options = {}) => {
    const { 
      params = {}, 
      useCache = true, 
      forceFresh = false,
      cacheKey = null,
      cacheTTL = CACHE_TTL
    } = options;
    
    // Generate cache key - use provided cacheKey or default generator
    const finalCacheKey = cacheKey || generateCacheKey(url, params);
    
    // Check cache if useCache is true and not forcing fresh data
    if (useCache && !forceFresh) {
      const cachedResponse = apiCache.get(finalCacheKey);
      
      if (cachedResponse && cachedResponse.expiry > Date.now()) {
        console.log(`[API Bridge] Using cached response for: ${url} (key: ${finalCacheKey})`);
        return cachedResponse.data;
      }
    }
    
    // Validate URL before making request
    if (!url) {
      console.error('[API Bridge] Attempted to make GET request with undefined URL');
      throw new Error('API endpoint URL is undefined');
    }
    
    // Make API request
    try {
      if (forceFresh) {
        console.log(`[API Bridge] Making FRESH GET request to: ${url} (bypassing cache)`, params);
      } else {
        // Making GET request
      }
      
      const response = await api.get(url, { params });
      
      // Cache the response if useCache is true and not forcing fresh
      if (useCache && !forceFresh) {
        apiCache.set(finalCacheKey, {
          data: response,
          expiry: Date.now() + cacheTTL
        });
        console.log(`[API Bridge] Cached response for: ${url} (key: ${finalCacheKey}, TTL: ${cacheTTL}ms)`);
      } else if (forceFresh) {
        console.log(`[API Bridge] Skipping cache due to forceFresh for: ${url}`);
      }
      
      return response;
    } catch (error) {
      console.error(`[API Bridge] Error in GET request to ${url}:`, error);
      
      // Handle error and show toast notification
      toast.error(errorHandlers.getErrorMessage(error));
      
      throw error;
    }
  },
  
  /**
   * Make a POST request
   * @param {string} url - API endpoint URL
   * @param {Object} data - Request payload
   * @param {Object} config - Request config
   * @returns {Promise} - API response
   */
  post: async (url, data = {}, config = {}) => {
    // Validate URL before making request
    if (!url) {
      console.error('[API Bridge] Attempted to make POST request with undefined URL');
      throw new Error('API endpoint URL is undefined');
    }

    // Extract cache clearing options from config
    const { clearCacheEndpoint, ...requestConfig } = config;

    // Special handling for FormData uploads
    if (data instanceof FormData) {
      // Remove Content-Type header for FormData - let browser set it with boundary
      requestConfig.headers = {
        ...requestConfig.headers
      };
      delete requestConfig.headers['Content-Type'];
      console.log('[API Bridge] FormData detected in POST - removing Content-Type header for proper multipart boundary');
    }

    try {
      console.log(`[API Bridge] Making POST request to: ${url}`, data);
      const response = await api.post(url, data, requestConfig);

      // PERFORMANCE FIX: Clear cache after successful POST
      if (response && clearCacheEndpoint) {
        console.log(`[API Bridge] Clearing cache for endpoint: ${clearCacheEndpoint}`);
        apiBridge.clearCache([clearCacheEndpoint, `${clearCacheEndpoint}/*`]);
      }

      return response;
    } catch (error) {
      console.error(`[API Bridge] Error in POST request to ${url}:`, error);

      // Handle error and show toast notification
      toast.error(errorHandlers.getErrorMessage(error));

      throw error;
    }
  },
  
  /**
   * Make a PUT request
   * @param {string} url - API endpoint URL
   * @param {Object} data - Request payload
   * @param {Object} config - Request config
   * @returns {Promise} - API response
   */
  put: async (url, data = {}, config = {}) => {
    // Validate URL before making request
    if (!url) {
      console.error('[API Bridge] Attempted to make PUT request with undefined URL');
      throw new Error('API endpoint URL is undefined');
    }

    // Extract cache clearing options from config
    const { clearCacheEndpoint, ...requestConfig } = config;

    try {
      console.log(`[API Bridge] Making PUT request to: ${url}`, data);
      const response = await api.put(url, data, requestConfig);

      // PERFORMANCE FIX: Clear cache after successful PUT
      if (response && clearCacheEndpoint) {
        console.log(`[API Bridge] Clearing cache for endpoint: ${clearCacheEndpoint}`);
        apiBridge.clearCache([clearCacheEndpoint, `${clearCacheEndpoint}/*`]);
      }

      return response;
    } catch (error) {
      console.error(`[API Bridge] Error in PUT request to ${url}:`, error);

      // Handle error and show toast notification
      toast.error(errorHandlers.getErrorMessage(error));

      throw error;
    }
  },
  
  /**
   * Make a PATCH request
   * @param {string} url - API endpoint URL
   * @param {Object} data - Request payload
   * @param {Object} config - Request config
   * @returns {Promise} - API response
   */
  patch: async (url, data = {}, config = {}) => {
    // Validate URL before making request
    if (!url) {
      console.error('[API Bridge] Attempted to make PATCH request with undefined URL');
      throw new Error('API endpoint URL is undefined');
    }

    // Extract cache clearing options from config
    const { clearCacheEndpoint, ...requestConfig } = config;

    try {
      console.log(`[API Bridge] Making PATCH request to: ${url}`, data);
      const response = await api.patch(url, data, requestConfig);

      // PERFORMANCE FIX: Clear cache after successful PATCH
      if (response && clearCacheEndpoint) {
        console.log(`[API Bridge] Clearing cache for endpoint: ${clearCacheEndpoint}`);
        apiBridge.clearCache([clearCacheEndpoint, `${clearCacheEndpoint}/*`]);
      }

      return response;
    } catch (error) {
      console.error(`[API Bridge] Error in PATCH request to ${url}:`, error);

      // Handle error and show toast notification
      toast.error(errorHandlers.getErrorMessage(error));

      throw error;
    }
  },
  
  /**
   * Make a DELETE request
   * @param {string} url - API endpoint URL
   * @param {Object} data - Request data (body)
   * @param {Object} config - Additional config
   * @returns {Promise<Object>} Response data
   */
  delete: async (url, data = {}, config = {}) => {
    if (!url) {
      console.error('[API Bridge] Attempted to make DELETE request with undefined URL');
      throw new Error('API endpoint URL is undefined');
    }

    // Extract cache clearing options from config
    const { clearCacheEndpoint, ...requestConfig } = config;

    try {
      console.log(`[API Bridge] Making DELETE request to: ${url}`, data);

      // Configure axios to send data in the body of DELETE request
      const deleteConfig = {
        ...requestConfig,
        data: data // This is the key - adding data to the config object
      };

      const response = await api.delete(url, deleteConfig);

      // PERFORMANCE FIX: Clear cache after successful delete
      if (response && clearCacheEndpoint) {
        console.log(`[API Bridge] Clearing cache for endpoint: ${clearCacheEndpoint}`);
        apiBridge.clearCache([clearCacheEndpoint, `${clearCacheEndpoint}/*`]);
      }

      return response;
    } catch (error) {
      console.error(`[API Bridge] Error in DELETE request to ${url}:`, error);

      // Handle error and show toast notification
      toast.error(errorHandlers.getErrorMessage(error));

      throw error;
    }
  },
  
  /**
   * Format API response using the appropriate formatter
   * @param {Object} response - API response
   * @param {string} type - Response type
   * @returns {Object} Formatted response
   */
  formatResponse: (response, type) => {
    if (!response || !response.data) return null;
    
    switch (type) {
      case 'shop':
        return responseFormatters.formatShopResponse(response);
      case 'user':
        return responseFormatters.formatUserResponse(response);
      default:
        return responseFormatters.formatGenericResponse(response, type);
    }
  },
  
  // Expose existing API modules with enhanced functionality
  auth: {
    ...authAPI,
    
    // Enhanced methods with standardized endpoints and error handling
    login: async (email, password) => {
      try {
        const response = await apiBridge.post(ENDPOINTS.AUTH.LOGIN, { email, password });
        
        // If login successful and response contains tokens, store them
        if (response?.data?.success && response?.data?.data) {
          const { accessToken, refreshToken } = response.data.data;
          if (accessToken && refreshToken) {
            apiBridge.token.setTokens(accessToken, refreshToken);
          }
        }
        
        return response;
      } catch (error) {
        console.error('[API Bridge] Login error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    },
    
    logout: async () => {
      try {
        const response = await apiBridge.post(ENDPOINTS.AUTH.LOGOUT);
        // Clear tokens regardless of response
        apiBridge.token.clearTokens();
        return response;
      } catch (error) {
        console.error('[API Bridge] Logout error:', error);
        // Still clear tokens even if API call fails
        apiBridge.token.clearTokens();
        throw error;
      }
    },
    
    logoutAll: async () => {
      try {
        const response = await apiBridge.post(ENDPOINTS.AUTH.LOGOUT_ALL);
        // Clear tokens regardless of response
        apiBridge.token.clearTokens();
        return response;
      } catch (error) {
        console.error('[API Bridge] Logout all error:', error);
        // Still clear tokens even if API call fails
        apiBridge.token.clearTokens();
        throw error;
      }
    },
    
    forgotPassword: async (email) => {
      try {
        const response = await apiBridge.post(ENDPOINTS.AUTH.FORGOT_PASSWORD, { email });
        if (response?.data?.success) {
          toast.success('Password reset instructions sent to your email');
        }
        return response;
      } catch (error) {
        console.error('[API Bridge] Forgot password error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    },
    
    resetPassword: async (token, newPassword, confirmPassword) => {
      try {
        const response = await apiBridge.post(ENDPOINTS.AUTH.RESET_PASSWORD, { 
          token, 
          newPassword, 
          confirmPassword 
        });
        
        if (response?.data?.success) {
          toast.success('Password has been reset successfully');
        }
        
        return response;
      } catch (error) {
        console.error('[API Bridge] Reset password error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    },
    
    changePassword: async (currentPassword, newPassword) => {
      try {
        const response = await apiBridge.post(ENDPOINTS.AUTH.CHANGE_PASSWORD, { 
          currentPassword, 
          newPassword 
        });
        
        if (response?.data?.success) {
          toast.success('Password changed successfully');
        }
        
        return response;
      } catch (error) {
        console.error('[API Bridge] Change password error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    },
    
    getProfile: async () => {
      try {
        const response = await apiBridge.get(ENDPOINTS.AUTH.PROFILE, { useCache: true });
        return response;
      } catch (error) {
        console.error('[API Bridge] Get profile error:', error);
        // Don't show toast for profile errors as they might happen during auth checks
        throw error;
      }
    }
  },
  
  shops: {
    ...shopAPI,
    
    // Enhanced methods with standardized endpoints and error handling
    getShops: async (params = {}) => {
      try {
        const response = await apiBridge.get(ENDPOINTS.SHOPS.BASE, { 
          params,
          useCache: true 
        });
        return apiBridge.formatResponse(response, 'shop');
      } catch (error) {
        console.error('[API Bridge] Get shops error:', error);
        
        // Return empty data rather than throwing to avoid breaking UI
        return {
          shops: [],
          pagination: {
            currentPage: 1,
            totalPages: 1,
            total: 0
          }
        };
      }
    },
    
    getShopById: async (shopId) => {
      try {
        const response = await apiBridge.get(ENDPOINTS.SHOPS.DETAIL(shopId), {
          useCache: true
        });
        const formatted = apiBridge.formatResponse(response, 'shop');
        return formatted?.shop || null;
      } catch (error) {
        console.error(`[API Bridge] Get shop ${shopId} error:`, error);
        throw error;
      }
    },
    
    createShop: async (shopData) => {
      try {
        const response = await apiBridge.post(ENDPOINTS.SHOPS.BASE, shopData, {
          clearCacheEndpoint: ENDPOINTS.SHOPS.BASE
        });
        const formatted = apiBridge.formatResponse(response, 'shop');
        
        if (formatted?.shop) {
          toast.success('Shop created successfully');
          return formatted.shop;
        }
        
        throw new Error('Failed to create shop');
      } catch (error) {
        console.error('[API Bridge] Create shop error:', error);
        throw error;
      }
    }
  },
  
  users: {
    ...userAPI,
    
    // Enhanced methods with standardized endpoints and error handling
    getUsers: async (filters = {}, page = 1, limit = 10) => {
      try {
        // Combine filters with pagination
        const params = { ...filters, page, limit };
        
        const response = await apiBridge.get(ENDPOINTS.USERS.BASE, { 
          params,
          useCache: true 
        });
        
        return apiBridge.formatResponse(response, 'user');
      } catch (error) {
        console.error('[API Bridge] Get users error:', error);
        
        // Return empty data rather than throwing to avoid breaking UI
        return {
          users: [],
          pagination: {
            currentPage: 1,
            totalPages: 1,
            total: 0
          }
        };
      }
    },
    
    getUserById: async (userId) => {
      try {
        const response = await apiBridge.get(ENDPOINTS.USERS.DETAIL(userId), {
          useCache: true
        });
        
        const formatted = apiBridge.formatResponse(response, 'user');
        return formatted?.user || null;
      } catch (error) {
        console.error(`[API Bridge] Get user ${userId} error:`, error);
        throw error;
      }
    },
    
    createUser: async (userData) => {
      try {
        // Check if superAdmin user has a shopId, as required
        if (userData.role === 'superAdmin' && (!userData.shopId || userData.shopId === 'no-shop')) {
          toast.error('SuperAdmin users must be associated with a shop');
          throw new Error('SuperAdmin users must be associated with a shop');
        }
        
        const response = await apiBridge.post(ENDPOINTS.USERS.BASE, userData, {
          clearCacheEndpoint: ENDPOINTS.USERS.BASE
        });
        
        const formatted = apiBridge.formatResponse(response, 'user');
        
        if (formatted?.user) {
          toast.success('User created successfully');
          return formatted.user;
        }
        
        throw new Error('Failed to create user');
      } catch (error) {
        console.error('[API Bridge] Create user error:', error);
        throw error;
      }
    },
    
    updateUser: async (userId, userData) => {
      try {
        // Check if superAdmin user has a shopId, as required
        if (userData.role === 'superAdmin' && (!userData.shopId || userData.shopId === 'no-shop')) {
          toast.error('SuperAdmin users must be associated with a shop');
          throw new Error('SuperAdmin users must be associated with a shop');
        }
        
        const response = await apiBridge.put(ENDPOINTS.USERS.DETAIL(userId), userData, {
          clearCacheEndpoint: ENDPOINTS.USERS.BASE
        });
        
        const formatted = apiBridge.formatResponse(response, 'user');
        
        if (formatted?.user) {
          toast.success('User updated successfully');
          return formatted.user;
        }
        
        throw new Error('Failed to update user');
      } catch (error) {
        console.error(`[API Bridge] Update user ${userId} error:`, error);
        throw error;
      }
    }
  },
  
  notifications: {
    ...notificationAPI,
    
    // Enhanced methods with standardized endpoints and error handling
    sendToShops: async (notificationData) => {
      try {
        const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.PUSH.TO_SHOPS, notificationData);
        
        if (response?.data?.success) {
          const successful = response.data.data?.successful || 0;
          const total = response.data.data?.totalShops || 0;
          toast.success(`Notification sent to ${successful}/${total} shops`);
        }
        
        return response;
      } catch (error) {
        console.error('[API Bridge] Send notification to shops error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    },
    
    sendBroadcast: async (notificationData) => {
      try {
        const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.PUSH.BROADCAST, notificationData);
        
        if (response?.data?.success) {
          toast.success('Broadcast notification sent successfully');
        }
        
        return response;
      } catch (error) {
        console.error('[API Bridge] Send broadcast notification error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    },
    
    sendDebtReminders: async (reminderData) => {
      try {
        const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.PUSH.DEBT_REMINDERS, reminderData);
        
        if (response?.data?.success) {
          toast.success('Debt reminder notifications sent successfully');
        }
        
        return response;
      } catch (error) {
        console.error('[API Bridge] Send debt reminders error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    },
    
    getNotificationStats: async (options = {}) => {
      try {
        const response = await apiBridge.get(ENDPOINTS.NOTIFICATIONS.PUSH.STATS, {
          params: options,
          useCache: true,
          cacheKey: `notification-stats-${options.shopId || 'all'}-${options.days || 30}`,
          cacheTTL: 300000 // 5 minutes
        });
        
        return apiBridge.formatResponse(response, 'notification');
      } catch (error) {
        console.error('[API Bridge] Get notification stats error:', error);
        
        // Return empty stats rather than throwing to avoid breaking UI
        return {
          stats: {},
          details: []
        };
      }
    },
    
    testFirebaseConnection: async () => {
      try {
        const response = await apiBridge.get(ENDPOINTS.NOTIFICATIONS.PUSH.TEST, { useCache: false });
        
        if (response?.data?.success) {
          toast.success('Firebase connection test successful');
        }
        
        return response;
      } catch (error) {
        console.error('[API Bridge] Firebase connection test error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    },
    
    getNotificationTargets: async () => {
      try {
        const response = await apiBridge.get(ENDPOINTS.NOTIFICATIONS.PUSH.TARGETS, {
          useCache: true,
          cacheKey: 'notification-targets',
          cacheTTL: 600000 // 10 minutes
        });
        
        return apiBridge.formatResponse(response, 'notification');
      } catch (error) {
        console.error('[API Bridge] Get notification targets error:', error);
        
        // Return empty targets rather than throwing to avoid breaking UI
        return {
          shops: [],
          users: [],
          total: 0
        };
      }
    },
    
    // FCM Token management methods (Backend-matched - only available endpoints)
    registerFCMToken: async (tokenData) => {
      try {
        const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.FCM.REGISTER, tokenData);
        
        if (response?.data?.success) {
          toast.success('FCM token registered successfully');
        }
        
        return response;
      } catch (error) {
        console.error('[API Bridge] Register FCM token error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    },
    
    sendTestNotification: async () => {
      try {
        const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.FCM.TEST, {});
        
        if (response?.data?.success) {
          toast.success('Test notification sent successfully');
        }
        
        return response;
      } catch (error) {
        console.error('[API Bridge] Send test notification error:', error);
        toast.error(errorHandlers.getErrorMessage(error));
        throw error;
      }
    }
    
    // Note: The following FCM methods are NOT available in backend:
    // - unregisterFCMToken (no DELETE /api/fcm/unregister route)
    // - getUserFCMTokens (no GET /api/fcm/tokens route)
    // - updateFCMTokenUsage, cleanupExpiredTokens also not available
  }
};

export default apiBridge;

// Export apiRequest as an alias for backward compatibility
export const apiRequest = async (url, options = {}) => {
  const { method = 'GET', ...config } = options;
  
  // Special handling for FormData to ensure proper Content-Type
  if (config.data instanceof FormData) {
    // Remove Content-Type header for FormData - let browser set it with boundary
    config.headers = {
      ...config.headers
    };
    delete config.headers['Content-Type'];
    console.log('[API Bridge] FormData detected - removing Content-Type header for proper multipart boundary');
  }
  
  switch (method.toUpperCase()) {
    case 'GET':
      return await apiBridge.get(url, config);
    case 'POST':
      return await apiBridge.post(url, config.data || config.body, config);
    case 'PUT':
      return await apiBridge.put(url, config.data || config.body, config);
    case 'PATCH':
      return await apiBridge.patch(url, config.data || config.body, config);
    case 'DELETE':
      return await apiBridge.delete(url, config.data || config.body, config);
    default:
      throw new Error(`Unsupported HTTP method: ${method}`);
  }
};
