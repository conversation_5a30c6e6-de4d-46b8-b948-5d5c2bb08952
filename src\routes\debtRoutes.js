const express = require('express');
const router = express.Router();

// Middleware
const { authenticate, authorize, checkModulePermission } = require('../middleware/authMiddleware');
const { validate, validateQuery, validateParams } = require('../middleware/validationMiddleware');
const { debtValidation } = require('../validations/schemas/debtSchemas');

// Controllers
const createDebt = require('../controllers/debt/createDebt');
const addPayment = require('../controllers/debt/addPayment');
const getAllDebts = require('../controllers/debt/getAllDebts');
const getDebtById = require('../controllers/debt/getDebtById');
const updateDebt = require('../controllers/debt/updateDebt');
const deleteDebt = require('../controllers/debt/deleteDebt');
const getDebtStats = require('../controllers/debt/getDebtStats');
const getPaymentHistory = require('../controllers/debt/getPaymentHistory');

/**
 * @route   POST /api/debts
 * @desc    Create new debt record (Step 1: Customer Takes a Loan)
 * @access  Admin (Shop Owner)
 */
router.post('/', 
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('debtManagement', 'create'),
  validate(debtValidation.createDebt),
  createDebt
);

/**
 * @route   GET /api/debts
 * @desc    Get all debts for shop with ML risk levels
 * @access  Admin (Shop Owner)
 */
router.get('/', 
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('debtManagement', 'view'),
  validateQuery(debtValidation.getDebts),
  getAllDebts
);

/**
 * @route   GET /api/debts/stats
 * @desc    Get debt statistics and ML insights
 * @access  Admin (Shop Owner)
 */
router.get('/stats', 
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('debtManagement', 'view'),
  getDebtStats
);

/**
 * @route   GET /api/debts/:debtId
 * @desc    Get single debt with ML evaluation details
 * @access  Admin (Shop Owner)
 */
router.get('/:debtId', 
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('debtManagement', 'view'),
  validateParams(debtValidation.getDebtById),
  getDebtById
);

/**
 * @route   PUT /api/debts/:debtId
 * @desc    Update debt information
 * @access  Admin (Shop Owner)
 */
router.put('/:debtId',
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('debtManagement', 'update'),
  validateParams(debtValidation.updateDebt),
  validate(debtValidation.updateDebt),
  updateDebt
);

/**
 * @route   DELETE /api/debts/:debtId
 * @desc    Delete debt record (soft delete)
 * @access  Admin (Shop Owner)
 */
router.delete('/:debtId', 
  validateParams(debtValidation.deleteDebt),
  deleteDebt
);

/**
 * @route   GET /api/debts/:debtId/payments
 * @desc    Get payment history for debt
 * @access  Admin (Shop Owner)
 */
router.get('/:debtId/payments', 
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('debtManagement', 'view'),
  getPaymentHistory
);

/**
 * @route   POST /api/debts/:debtId/payments
 * @desc    Add payment to debt (Step 7: Customer Pays & System Updates)
 * @access  Admin (Shop Owner)
 */
router.post('/:debtId/payments', 
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('debtManagement', 'update'),
  validateParams(debtValidation.addPayment),
  validate(debtValidation.addPayment),
  addPayment
);

module.exports = router; 
