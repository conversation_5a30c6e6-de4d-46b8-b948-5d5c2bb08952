/**
 * Request throttling module
 * Prevents excessive API requests to the same endpoint
 */

// Store timestamps of requests to track throttling
const requestTimestamps = {};

// Throttle configuration per HTTP method (in milliseconds)
export const REQUEST_THROTTLE = {
  'GET': 1000,    // 1 second between identical GET requests (increased from 300ms)
  'POST': 500,    // 0.5 seconds between identical POST requests
  'PUT': 500,     // 0.5 seconds between identical PUT requests
  'DELETE': 500,  // 0.5 seconds between identical DELETE requests
  'default': 200  // Default throttle time
};

// Endpoints that should bypass throttling
export const THROTTLE_EXCEPTIONS = [
  '/api/shops',
  '/api/users',
  '/api/settings',
  '/api/subscriptions/stats',  // Add stats endpoint to exceptions
  '/api/subscriptions'         // Add main subscriptions endpoint to exceptions
];

/**
 * Check if a request should be throttled
 * @param {string} method - HTTP method (GET, POST, etc.)
 * @param {string} url - Request URL
 * @returns {Object} Result with shouldThrottle flag and timeSinceLastRequest
 */
export function shouldThrottleRequest(method, url) {
  // Create a request key for throttling
  const requestKey = `${method}:${url}`;
  const now = Date.now();
  const lastRequest = requestTimestamps[requestKey] || 0;
  const throttleTime = REQUEST_THROTTLE[method] || REQUEST_THROTTLE.default;
  const timeSinceLastRequest = now - lastRequest;
  
  // Check if this is an exception that should bypass throttling
  // Safely handle potentially undefined URLs
  const shouldBypassThrottle = url && THROTTLE_EXCEPTIONS.some(endpoint => {
    return url.includes(endpoint);
  }) || false;
  
  // Check if this request should be throttled (only if not in exceptions)
  const shouldThrottle = !shouldBypassThrottle && timeSinceLastRequest < throttleTime;
  
  if (shouldThrottle) {
    console.log(`[API] Throttling request to ${url} (${timeSinceLastRequest}ms since last request)`);
  }
  
  // Record this request timestamp if not throttled
  if (!shouldThrottle) {
    requestTimestamps[requestKey] = now;
  }
  
  return {
    shouldThrottle,
    timeSinceLastRequest,
    throttleTime
  };
}

/**
 * Create a throttling error response
 * @param {Object} config - The original request config
 * @returns {Object} Error object
 */
export function createThrottleError(config) {
  return {
    config,
    response: {
      status: 429,
      data: { message: 'Too many requests' }
    },
    message: 'Request throttled to prevent excessive API calls',
    throttled: true
  };
}
