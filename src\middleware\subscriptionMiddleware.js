/**
 * Subscription Access Control Middleware
 * Restricts mobile app access when subscriptions are expired
 * Specifically designed for Admin/Shop Owner roles using mobile applications
 */
const { Subscription, Shop } = require('../models');
const { AppError, logError, logInfo } = require('../utils');

/**
 * Check if user's subscription allows access to features
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object  
 * @param {Function} next - Express next middleware function
 */
const checkSubscriptionAccess = async (req, res, next) => {
  try {
    // Skip subscription check for superAdmin
    if (req.user && req.user.role === 'superAdmin') {
      return next();
    }

    // Only apply to admin and shop owner roles
    if (!req.user || !['admin', 'shopOwner'].includes(req.user.role)) {
      return next();
    }

    // Get shop ID from user or request
    const shopId = req.user.shopId || req.body.shopId || req.params.shopId || req.query.shopId;
    
    if (!shopId) {
      return res.status(400).json({
        success: false,
        error: 'Shop ID is required for subscription verification',
        code: 'SHOP_ID_REQUIRED',
        restrictedAccess: true
      });
    }

    // Get current subscription for the shop
    const currentSubscription = await Subscription.findOne({
      shopId: shopId,
      isDeleted: false
    }).sort({ createdAt: -1 }).lean();

    // If no subscription found
    if (!currentSubscription) {
      return res.status(403).json({
        success: false,
        error: 'No active subscription found. Please subscribe to access DeynCare features.',
        code: 'NO_SUBSCRIPTION',
        restrictedAccess: true,
        subscriptionStatus: 'none',
        actionRequired: 'subscribe',
                  paymentUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/plans`
      });
    }

    const now = new Date();
    const endDate = new Date(currentSubscription.dates?.endDate || currentSubscription.endDate);
    const isExpired = endDate < now;
    const daysRemaining = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));

    // Check subscription status and expiry
    if (currentSubscription.status === 'expired' || isExpired) {
      logInfo(`Access restricted for shop ${shopId} - subscription expired`, 'SubscriptionMiddleware');
      
      return res.status(403).json({
        success: false,
        error: 'Your subscription has expired. Please renew to continue using DeynCare.',
        code: 'SUBSCRIPTION_EXPIRED',
        restrictedAccess: true,
        subscriptionStatus: 'expired',
        subscriptionDetails: {
          planType: currentSubscription.plan?.type || 'unknown',
          expiredOn: endDate.toISOString(),
          daysExpired: Math.abs(daysRemaining)
        },
        actionRequired: 'renew',
        // Mobile App Integration - Popup Flag
        showRenewalPopup: true,
        popupConfig: {
          title: 'Subscription Expired',
          message: `Your ${currentSubscription.plan?.type || 'subscription'} plan expired ${Math.abs(daysRemaining)} days ago`,
          urgent: Math.abs(daysRemaining) > 30,
          primaryAction: 'renew_now',
          secondaryAction: 'view_plans'
        },
        // Payment Methods Integration (using existing system)
        renewalEndpoint: '/api/subscriptions/renew',
        paymentMethodsEndpoint: '/api/subscriptions/payment-methods', 
        restrictedFeatures: [
          'customer_management',
          'debt_tracking',
          'payment_processing', 
          'risk_assessment',
          'reporting',
          'data_export'
        ]
      });
    }

    // Check if subscription is inactive/canceled
    if (['canceled', 'inactive'].includes(currentSubscription.status)) {
      return res.status(403).json({
        success: false,
        error: 'Your subscription is inactive. Please reactivate to continue using DeynCare.',
        code: 'SUBSCRIPTION_INACTIVE',
        restrictedAccess: true,
        subscriptionStatus: currentSubscription.status,
        actionRequired: 'reactivate',
                  paymentUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/reactivate`
      });
    }

    // Warning for expiring soon (less than 3 days)
    if (daysRemaining <= 3 && daysRemaining > 0) {
      // Add warning to response but allow access
      res.locals.subscriptionWarning = {
        type: 'expiring_soon',
        daysRemaining,
        message: `Your subscription expires in ${daysRemaining} day${daysRemaining !== 1 ? 's' : ''}`,
        renewUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/renew`,
        evcPaymentUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/renew?method=evc`
      };
    }

    // Add subscription info to request for use in other middleware/controllers
    req.subscription = {
      id: currentSubscription._id,
      status: currentSubscription.status,
      planType: currentSubscription.plan?.type,
      endDate: endDate,
      daysRemaining: Math.max(0, daysRemaining),
      isActive: currentSubscription.status === 'active' && !isExpired,
      autoRenew: currentSubscription.renewalSettings?.autoRenew || false
    };

    next();
  } catch (error) {
    logError('Error checking subscription access', 'SubscriptionMiddleware', error);
    
    // In case of error, allow access but log the issue
    next();
  }
};

/**
 * Middleware specifically for mobile app endpoints
 * Stricter checking with mobile-specific response format
 */
const checkMobileSubscriptionAccess = async (req, res, next) => {
  try {
    // Set mobile app flag
    req.isMobileApp = true;
    
    // Use the main subscription check
    await checkSubscriptionAccess(req, res, (error) => {
      if (error) {
        return next(error);
      }
      
      // If subscription warning exists, include it in all mobile responses
      if (res.locals.subscriptionWarning) {
        const originalSend = res.json;
        res.json = function(data) {
          if (data && typeof data === 'object') {
            data.subscriptionWarning = res.locals.subscriptionWarning;
          }
          return originalSend.call(this, data);
        };
      }
      
      next();
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get subscription status for mobile app
 * Returns detailed subscription information
 */
const getSubscriptionStatus = async (req, res, next) => {
  try {
    const shopId = req.user?.shopId || req.params.shopId || req.query.shopId;
    
    if (!shopId) {
      return res.status(400).json({
        success: false,
        error: 'Shop ID is required'
      });
    }

    const subscription = await Subscription.findOne({
      shopId: shopId,
      isDeleted: false
    }).sort({ createdAt: -1 });

    if (!subscription) {
      return res.json({
        success: true,
        hasSubscription: false,
        status: 'none',
        accessLevel: 'restricted',
        actionRequired: 'subscribe',
        message: 'No subscription found. Please subscribe to access features.'
      });
    }

    const now = new Date();
    const endDate = new Date(subscription.dates?.endDate || subscription.endDate);
    const isExpired = endDate < now;
    const daysRemaining = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));

    const status = isExpired ? 'expired' : subscription.status;
    const accessLevel = (status === 'active' && !isExpired) ? 'full' : 'restricted';

    res.json({
      success: true,
      hasSubscription: true,
      status: status,
      accessLevel: accessLevel,
      subscription: {
        id: subscription._id,
        planType: subscription.plan?.type || 'unknown',
        startDate: subscription.dates?.startDate || subscription.startDate,
        endDate: endDate,
        daysRemaining: Math.max(0, daysRemaining),
        autoRenew: subscription.renewalSettings?.autoRenew || false,
        isExpired: isExpired
      },
      actionRequired: accessLevel === 'restricted' ? (status === 'expired' ? 'renew' : 'reactivate') : null,
      paymentOptions: {
                  renewUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/renew`,
          evcPaymentUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/renew?method=evc`,
          plansUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/plans`
      },
      restrictedFeatures: accessLevel === 'restricted' ? [
        'customer_management',
        'debt_tracking',
        'payment_processing',
        'risk_assessment', 
        'reporting',
        'data_export'
      ] : []
    });
  } catch (error) {
    logError('Error getting subscription status', 'SubscriptionMiddleware', error);
    next(error);
  }
};

module.exports = {
  checkSubscriptionAccess,
  checkMobileSubscriptionAccess,
  getSubscriptionStatus
}; 