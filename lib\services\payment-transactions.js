/**
 * Payment Transactions Service
 * Handles all payment transaction related API operations for SuperAdmin
 * UPDATED: Now uses ENDPOINTS from contract instead of hardcoded URLs
 */

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, handleSuccess, processApiResponse } from './baseService';

/**
 * Get all payment transactions with filters and pagination
 * Automatically filters for subscription context only
 */
async function getTransactions(params = {}) {
  try {
    // Ensure we only get subscription payments
    const filteredParams = {
      ...params,
      paymentContext: 'subscription'
    };

    const response = await apiBridge.get(ENDPOINTS.PAYMENT_TRANSACTIONS.BASE, {
      params: filteredParams,
      useCache: true
    });

    return processApiResponse(response, 'Payment transactions fetched successfully');
  } catch (error) {
    handleError(error, 'PaymentTransactionsService.getTransactions', true);
    throw error;
  }
}

/**
 * Get payment transaction statistics
 */
async function getTransactionStats(params = {}) {
  try {
    const response = await apiBridge.get(ENDPOINTS.PAYMENT_TRANSACTIONS.STATS, {
      params,
      useCache: true
    });

    return processApiResponse(response, 'Payment transaction stats fetched successfully');
  } catch (error) {
    handleError(error, 'PaymentTransactionsService.getTransactionStats', true);
    throw error;
  }
}

/**
 * Get single payment transaction by ID
 */
async function getTransactionById(paymentId) {
  try {
    if (!paymentId) {
      throw new Error('Payment ID is required');
    }

    const response = await apiBridge.get(ENDPOINTS.PAYMENT_TRANSACTIONS.DETAIL(paymentId), {
      useCache: true
    });

    return processApiResponse(response, 'Payment transaction fetched successfully');
  } catch (error) {
    handleError(error, 'PaymentTransactionsService.getTransactionById', true);
    throw error;
  }
}

/**
 * Approve payment transaction
 */
async function approveTransaction(paymentId, adminNote) {
  try {
    if (!paymentId) {
      throw new Error('Payment ID is required');
    }
    
    if (!adminNote) {
      throw new Error('Admin note is required for approval');
    }

    const response = await apiBridge.post(ENDPOINTS.PAYMENT_TRANSACTIONS.APPROVE(paymentId), {
      adminNote
    });

    return processApiResponse(response, 'Payment transaction approved successfully');
  } catch (error) {
    handleError(error, 'PaymentTransactionsService.approveTransaction', true);
    throw error;
  }
}

/**
 * Reject payment transaction
 */
async function rejectTransaction(paymentId, adminNote) {
  try {
    if (!paymentId) {
      throw new Error('Payment ID is required');
    }
    
    if (!adminNote) {
      throw new Error('Admin note is required for rejection');
    }

    const response = await apiBridge.post(ENDPOINTS.PAYMENT_TRANSACTIONS.REJECT(paymentId), {
      adminNote
    });

    return processApiResponse(response, 'Payment transaction rejected successfully');
  } catch (error) {
    handleError(error, 'PaymentTransactionsService.rejectTransaction', true);
    throw error;
  }
}

/**
 * Export payment transactions
 */
async function exportTransactions(params = {}) {
  try {
    const response = await apiBridge.get(ENDPOINTS.PAYMENT_TRANSACTIONS.EXPORT, {
      params,
      responseType: 'blob'
    });

    return response.data;
  } catch (error) {
    handleError(error, 'PaymentTransactionsService.exportTransactions', true);
    throw error;
  }
}

/**
 * Format payment method for display
 */
function formatPaymentMethod(method) {
  if (!method) return 'Unknown';

  const methodMap = {
    'cash': 'Cash',
    'bank_transfer': 'Bank Transfer',
    'mobile_money': 'Mobile Money',
    'card': 'Card',
    'other': 'Other',
    'offline': 'Offline Payment',
    'evc_plus': 'EVC Plus',
    'evc': 'EVC Plus', // Alternative naming
    'credit_card': 'Credit Card',
    'debit_card': 'Debit Card'
  };

  return methodMap[method.toLowerCase()] || method.charAt(0).toUpperCase() + method.slice(1);
}

/**
 * Format currency value
 */
function formatCurrency(amount, currency = 'USD') {
  if (amount === null || amount === undefined || isNaN(amount)) return '$0.00';

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * Format transaction status for display
 */
function formatStatus(status) {
  if (!status) return 'Unknown';

  const statusMap = {
    'pending': 'Pending',
    'processing': 'Processing',
    'success': 'Success',
    'failed': 'Failed',
    'approved': 'Approved',
    'rejected': 'Rejected',
    'completed': 'Completed'
  };

  return statusMap[status.toLowerCase()] || status.charAt(0).toUpperCase() + status.slice(1);
}

/**
 * Get status variant for badge styling
 */
function getStatusVariant(status) {
  if (!status) return 'secondary';

  const variantMap = {
    'pending': 'secondary',
    'processing': 'secondary',
    'success': 'default',
    'failed': 'destructive',
    'approved': 'default',
    'rejected': 'destructive',
    'completed': 'default'
  };

  return variantMap[status.toLowerCase()] || 'secondary';
}

/**
 * Format date for display
 */
function formatDate(dateString) {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid Date';

    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(date);
  } catch (error) {
    return 'Invalid Date';
  }
}

const PaymentTransactionsService = {
  getTransactions,
  getTransactionStats,
  getTransactionById,
  approveTransaction,
  rejectTransaction,
  exportTransactions,

  // Utility functions
  formatPaymentMethod,
  formatCurrency,
  formatStatus,
  getStatusVariant,
  formatDate,

  // Backward compatibility methods
  async getAllPaymentTransactions(params = {}) {
    return getTransactions(params);
  }
};

export default PaymentTransactionsService; 